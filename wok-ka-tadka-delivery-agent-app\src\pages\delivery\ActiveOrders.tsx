import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ArrowLeft, Clock, Users, CheckCircle, AlertCircle, Utensils, ChefHat, Timer } from "lucide-react";

const ActiveOrders = () => {
  const navigate = useNavigate();
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(false);
  
  // Mock data for active table orders
  const [activeOrders] = useState([
    {
      id: "KOT001",
      tableNumber: 3,
      customerCount: 4,
      items: [
        { name: "Chicken Biryani", quantity: 2, status: "preparing" },
        { name: "Paneer Butter Masala", quantity: 1, status: "ready" },
        { name: "Gar<PERSON>", quantity: 3, status: "preparing" }
      ],
      totalAmount: 850,
      orderTime: "12:30 PM",
      status: "preparing",
      waitTime: "15 mins"
    },
    {
      id: "KOT002",
      tableNumber: 7,
      customerCount: 2,
      items: [
        { name: "Mutton Curry", quantity: 1, status: "ready" },
        { name: "Jeera Rice", quantity: 2, status: "ready" }
      ],
      totalAmount: 450,
      orderTime: "12:45 PM",
      status: "ready",
      waitTime: "5 mins"
    },
    {
      id: "KOT003",
      tableNumber: 5,
      customerCount: 3,
      items: [
        { name: "Veg Hakka Noodles", quantity: 1, status: "preparing" },
        { name: "Chilli Chicken", quantity: 1, status: "preparing" },
        { name: "Hot & Sour Soup", quantity: 2, status: "served" }
      ],
      totalAmount: 620,
      orderTime: "1:00 PM",
      status: "preparing",
      waitTime: "10 mins"
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "preparing": return "bg-warning text-warning-foreground";
      case "ready": return "bg-success text-success-foreground";
      case "served": return "bg-primary text-primary-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "preparing": return "Preparing";
      case "ready": return "Ready to Serve";
      case "served": return "Served";
      default: return status;
    }
  };

  const handleCheckStatus = async (order) => {
    setCheckingStatus(true);

    // Simulate API call to check kitchen status
    setTimeout(() => {
      setSelectedOrder(order);
      setShowStatusDialog(true);
      setCheckingStatus(false);
    }, 800);
  };

  const getEstimatedTime = (order) => {
    const preparingItems = order.items.filter(item => item.status === "preparing").length;
    const baseTime = 5; // 5 minutes base time
    return baseTime + (preparingItems * 3); // 3 minutes per preparing item
  };

  const getItemStatusColor = (status: string) => {
    switch (status) {
      case "preparing": return "text-orange-600 bg-orange-50";
      case "ready": return "text-green-600 bg-green-50";
      case "served": return "text-blue-600 bg-blue-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">Active Orders</h1>
              <p className="text-white/80 text-sm">Table orders in progress</p>
            </div>
          </div>
          <Badge className="bg-white/20 text-white border-white/30">
            {activeOrders.length} Active
          </Badge>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {activeOrders.length > 0 ? (
          activeOrders.map((order) => (
            <Card key={order.id} className="shadow-sm border-0 bg-white hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Utensils className="h-5 w-5 text-primary" />
                      <span className="font-bold text-lg">Table {order.tableNumber}</span>
                    </div>
                    <Badge className={`${getStatusColor(order.status)} text-xs px-2 py-1`}>
                      {getStatusText(order.status)}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">KOT {order.id}</p>
                    <p className="text-xs text-gray-400">{order.orderTime}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{order.customerCount} guests</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>Wait: {order.waitTime}</span>
                  </div>
                  <div className="font-semibold text-primary">₹{order.totalAmount}</div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* Order Items */}
                <div className="space-y-2 mb-4">
                  <h4 className="font-medium text-gray-900 text-sm">Order Items:</h4>
                  {order.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <span className="font-medium text-gray-900">{item.name}</span>
                        <span className="text-gray-600 ml-2">× {item.quantity}</span>
                      </div>
                      <Badge className={`${getItemStatusColor(item.status)} text-xs px-2 py-1 border-0`}>
                        {getStatusText(item.status)}
                      </Badge>
                    </div>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 border-primary text-primary hover:bg-primary hover:text-white"
                    onClick={() => navigate(`/delivery/table-details/${order.tableNumber}`)}
                  >
                    View Details
                  </Button>
                  {order.status === "ready" && (
                    <Button
                      variant="delivery"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        // Handle mark as served
                        alert(`Order for Table ${order.tableNumber} marked as served!`);
                      }}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Mark Served
                    </Button>
                  )}
                  {order.status === "preparing" && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-orange-500 text-orange-600 hover:bg-orange-500 hover:text-white"
                      onClick={() => handleCheckStatus(order)}
                      disabled={checkingStatus}
                    >
                      {checkingStatus ? (
                        <>
                          <Timer className="h-4 w-4 mr-1 animate-spin" />
                          Checking...
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-4 w-4 mr-1" />
                          Check Status
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <Utensils className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Orders</h3>
            <p className="text-gray-500 mb-6">All tables are currently available or orders have been served.</p>
            <Button
              variant="delivery"
              onClick={() => navigate("/delivery/tables")}
            >
              Manage Tables
            </Button>
          </div>
        )}
      </div>

      {/* Status Check Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5 text-orange-600" />
              Kitchen Status - Table {selectedOrder?.tableNumber}
            </DialogTitle>
          </DialogHeader>

          {selectedOrder && (
            <div className="space-y-4">
              {/* Order Summary */}
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">KOT {selectedOrder.id}</span>
                  <Badge className="bg-orange-100 text-orange-800">
                    {selectedOrder.status === "preparing" ? "In Kitchen" : selectedOrder.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>Ordered: {selectedOrder.orderTime}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Timer className="h-4 w-4" />
                    <span>Est: {getEstimatedTime(selectedOrder)} mins</span>
                  </div>
                </div>
              </div>

              {/* Item Status */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Item Progress:</h4>
                <div className="space-y-2">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-white border rounded-lg">
                      <div className="flex-1">
                        <span className="font-medium text-gray-900">{item.name}</span>
                        <span className="text-gray-600 ml-2">× {item.quantity}</span>
                      </div>
                      <Badge
                        className={`text-xs px-2 py-1 ${
                          item.status === "preparing"
                            ? "bg-orange-100 text-orange-800"
                            : item.status === "ready"
                            ? "bg-green-100 text-green-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {item.status === "preparing" ? "Cooking" :
                         item.status === "ready" ? "Ready" : "Served"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => setShowStatusDialog(false)}
                >
                  Close
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="flex-1 bg-orange-600 hover:bg-orange-700"
                  onClick={() => {
                    // Refresh status - in real app this would fetch from API
                    setShowStatusDialog(false);
                    // Show a toast or update the order status
                    alert(`Status refreshed for Table ${selectedOrder.tableNumber}`);
                  }}
                >
                  <Timer className="h-4 w-4 mr-1" />
                  Refresh Status
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ActiveOrders;
