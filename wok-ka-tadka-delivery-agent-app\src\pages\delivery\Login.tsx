
import { useState } from "react";
import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone, Lock, UserCheck, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Logo } from "@/components/ui/logo";
import { validateStaffPin, initializeDefaultPins, getAllStaffPins } from "@/utils/staffPinStorage";

const Login = () => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [pin, setPin] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [phoneError, setPhoneError] = useState("");
  const [userType, setUserType] = useState<"admin" | "waiter">("waiter");
  const navigate = useNavigate();
  const { toast } = useToast();

  // Initialize default PINs on component mount
  React.useEffect(() => {
    initializeDefaultPins();
  }, []);

  const validatePhoneNumber = (phone: string) => {
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length !== 10) {
      setPhoneError("Please enter exactly 10 digits");
      return false;
    }
    setPhoneError("");
    return true;
  };



  const handlePinLogin = async () => {
    if (!validatePhoneNumber(phoneNumber)) {
      toast({
        title: "Invalid phone number",
        description: "Please enter exactly 10 digits",
        variant: "destructive",
      });
      return;
    }

    if (!pin || pin.length !== 4) {
      toast({
        title: "Invalid PIN",
        description: "Please enter a valid 4-digit PIN",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // For admin login, use default admin credentials
    if (userType === "admin") {
      // Default admin credentials: phone: 1234567890, pin: 0000
      if (phoneNumber === "1234567890" && pin === "0000") {
        setTimeout(() => {
          setIsLoading(false);
          navigate("/admin/dashboard");
        }, 1000);
      } else {
        setIsLoading(false);
        toast({
          title: "Invalid credentials",
          description: "Please check your admin credentials",
          variant: "destructive",
        });
      }
      return;
    }

    // For waiter login, validate against staff PINs
    const staffMember = validateStaffPin(phoneNumber, pin);

    setTimeout(() => {
      setIsLoading(false);

      if (staffMember) {
        // Store staff info in localStorage for the session
        localStorage.setItem('currentStaff', JSON.stringify({
          id: staffMember.id,
          name: staffMember.name,
          phone: staffMember.phone,
          role: staffMember.role
        }));

        toast({
          title: "Login successful",
          description: `Welcome back, ${staffMember.name}!`,
        });

        navigate("/delivery/dashboard");
      } else {
        toast({
          title: "Invalid credentials",
          description: "Please check your phone number and PIN",
          variant: "destructive",
        });
      }
    }, 1000);
  };

  return (
    <div className="apk-page-container bg-gradient-primary flex items-center justify-center p-4">
      <div className="w-full max-w-md animate-fade-in my-auto">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4 animate-pulse-soft">
            <Logo size="lg" variant="default" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Wok Ka Tadka</h1>
          <p className="text-white/90 text-lg">Delivery Portal</p>
        </div>

        <Card className="bg-white/95 backdrop-blur-sm shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl text-gray-900">Welcome Back</CardTitle>
            <CardDescription className="text-gray-600">
              Sign in to {userType === "admin" ? "manage your restaurant" : "start your delivery shift"}
            </CardDescription>
          </CardHeader>
          <CardContent className="px-6 pb-6">
            {/* User Type Selection */}
            <div className="mb-6">
              <Label className="text-gray-700 mb-3 block">Select User Type</Label>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  type="button"
                  variant={userType === "waiter" ? "default" : "outline"}
                  className={`h-12 flex items-center justify-center gap-2 ${
                    userType === "waiter"
                      ? "bg-primary text-white"
                      : "border-gray-200 text-gray-600 hover:border-primary hover:text-primary"
                  }`}
                  onClick={() => setUserType("waiter")}
                >
                  <Users className="h-4 w-4" />
                  Waiter
                </Button>
                <Button
                  type="button"
                  variant={userType === "admin" ? "default" : "outline"}
                  className={`h-12 flex items-center justify-center gap-2 ${
                    userType === "admin"
                      ? "bg-primary text-white"
                      : "border-gray-200 text-gray-600 hover:border-primary hover:text-primary"
                  }`}
                  onClick={() => setUserType("admin")}
                >
                  <UserCheck className="h-4 w-4" />
                  Admin
                </Button>
              </div>
            </div>
            {/* Staff PIN Login Form */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="staff-phone" className="text-gray-700">Mobile Number</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="staff-phone"
                    type="tel"
                    placeholder="Enter 10-digit mobile number"
                    value={phoneNumber}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '');
                      if (value.length <= 10) {
                        setPhoneNumber(value);
                        if (value.length === 10) {
                          setPhoneError("");
                        }
                      }
                    }}
                    className={`pl-10 h-12 focus:border-primary ${phoneError ? 'border-red-500' : 'border-gray-200'}`}
                    maxLength={10}
                  />
                </div>
                {phoneError && (
                  <p className="text-red-500 text-sm">{phoneError}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="pin" className="text-gray-700">
                  {userType === "admin" ? "Admin PIN" : "Staff PIN"}
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="pin"
                    type="password"
                    placeholder={`Enter your 4-digit ${userType === "admin" ? "admin" : "staff"} PIN`}
                    value={pin}
                    onChange={(e) => setPin(e.target.value.replace(/\D/g, '').slice(0, 4))}
                    className="pl-10 h-12 border-gray-200 focus:border-primary text-center text-lg tracking-widest"
                    maxLength={4}
                  />
                </div>
                {userType === "admin" && (
                  <p className="text-xs text-gray-500">Default admin credentials: 1234567890 / 0000</p>
                )}
              </div>

              <Button
                variant="delivery"
                size="lg"
                className="w-full h-12 mt-6"
                onClick={handlePinLogin}
                disabled={isLoading || phoneNumber.length !== 10 || pin.length !== 4}
              >
                {isLoading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />}
                Login as {userType === "admin" ? "Admin" : "Staff"}
              </Button>
            </div>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Need help? <span className="text-primary font-medium cursor-pointer hover:underline">Contact your manager</span>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Login;
