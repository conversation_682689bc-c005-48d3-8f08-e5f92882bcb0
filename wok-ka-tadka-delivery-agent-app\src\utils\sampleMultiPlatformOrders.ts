// Sample data generator for restaurant orders
import {
  MultiPlatformOrder,
  OwnAppOrder,
  multiPlatformOrderManager
} from './multiPlatformOrders';

// Sample menu items for generating orders
const sampleMenuItems = [
  { id: '1', name: 'Chicken Biryani', price: 300 },
  { id: '2', name: '<PERSON>eer <PERSON>', price: 250 },
  { id: '3', name: '<PERSON><PERSON><PERSON>', price: 60 },
  { id: '4', name: '<PERSON>', price: 180 },
  { id: '5', name: 'Chicken Tikka', price: 320 },
  { id: '6', name: '<PERSON><PERSON>', price: 380 },
  { id: '7', name: 'Veg Fried <PERSON>', price: 160 },
  { id: '8', name: 'Butter <PERSON>', price: 340 },
  { id: '9', name: 'Tan<PERSON><PERSON> Roti', price: 40 },
  { id: '10', name: '<PERSON><PERSON>', price: 80 }
];

const sampleCustomers = [
  { name: '<PERSON><PERSON>', phone: '+91 9876543210', email: '<EMAIL>' },
  { name: '<PERSON><PERSON>', phone: '+91 9876543211', email: 'rahu<PERSON>@email.com' },
  { name: '<PERSON>', phone: '+91 9876543212', email: '<EMAIL>' },
  { name: 'Vikram Patel', phone: '+91 9876543213', email: '<EMAIL>' },
  { name: 'Sneha Gupta', phone: '+91 9876543214', email: '<EMAIL>' }
];

const sampleAddresses = [
  {
    street: '123 MG Road',
    area: 'Bandra West',
    city: 'Mumbai',
    pincode: '400050',
    landmark: 'Near Bandra Station'
  },
  {
    street: '456 Park Street',
    area: 'Andheri East',
    city: 'Mumbai',
    pincode: '400069',
    landmark: 'Opposite Metro Mall'
  },
  {
    street: '789 SV Road',
    area: 'Malad West',
    city: 'Mumbai',
    pincode: '400064',
    landmark: 'Near Infinity Mall'
  }
];

// Generate random order items
function generateRandomItems(): any[] {
  const numItems = Math.floor(Math.random() * 4) + 1; // 1-4 items
  const selectedItems = [];
  
  for (let i = 0; i < numItems; i++) {
    const item = sampleMenuItems[Math.floor(Math.random() * sampleMenuItems.length)];
    const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity
    
    selectedItems.push({
      id: item.id,
      name: item.name,
      quantity,
      price: item.price,
      total: item.price * quantity,
      customizations: Math.random() > 0.7 ? ['Less Spicy'] : undefined
    });
  }
  
  return selectedItems;
}



// Generate Own App order
function generateOwnAppOrder(): OwnAppOrder {
  const items = generateRandomItems();
  const subtotal = items.reduce((sum, item) => sum + item.total, 0);
  const taxes = Math.round(subtotal * 0.18);
  const deliveryFee = Math.random() > 0.5 ? 30 : 0; // Sometimes pickup
  const discount = Math.random() > 0.8 ? Math.round(subtotal * 0.1) : 0; // 10% discount sometimes
  const total = subtotal + taxes + deliveryFee - discount;
  const customer = sampleCustomers[Math.floor(Math.random() * sampleCustomers.length)];
  
  const orderId = `OWN${Date.now()}${Math.floor(Math.random() * 1000)}`;
  const platformOrderId = `WKT${Math.floor(Math.random() * 1000000)}`;
  
  const statuses = ['new', 'accepted', 'preparing', 'ready'];
  const status = statuses[Math.floor(Math.random() * statuses.length)] as any;
  
  const orderPlaced = new Date(Date.now() - Math.random() * 2 * 60 * 60 * 1000).toISOString();
  
  const isDineIn = Math.random() > 0.6;
  
  return {
    id: orderId,
    platform: 'own-app',
    platformOrderId,
    status,
    customer,
    items,
    delivery: {
      type: isDineIn ? 'dine-in' : (deliveryFee > 0 ? 'delivery' : 'pickup'),
      estimatedTime: isDineIn ? 15 : 25,
      deliveryFee,
      address: deliveryFee > 0 ? sampleAddresses[Math.floor(Math.random() * sampleAddresses.length)] : undefined
    },
    pricing: {
      subtotal,
      taxes,
      deliveryFee,
      discount,
      total
    },
    timestamps: {
      orderPlaced,
      ...(status !== 'new' && { accepted: new Date(new Date(orderPlaced).getTime() + 1 * 60 * 1000).toISOString() }),
      ...(status === 'preparing' && { preparing: new Date(new Date(orderPlaced).getTime() + 3 * 60 * 1000).toISOString() }),
      ...(status === 'ready' && { ready: new Date(new Date(orderPlaced).getTime() + 18 * 60 * 1000).toISOString() })
    },
    specialInstructions: Math.random() > 0.7 ? 'Make it extra spicy' : undefined,
    paymentStatus: Math.random() > 0.1 ? 'paid' : 'pending',
    paymentMethod: Math.random() > 0.5 ? 'UPI' : 'Cash',
    ownAppData: {
      tableNumber: isDineIn ? `Table ${Math.floor(Math.random() * 8) + 1}` : undefined,
      waiterAssigned: isDineIn ? ['Raj Kumar', 'Priya Singh', 'Amit Sharma'][Math.floor(Math.random() * 3)] : undefined,
      kotNumber: isDineIn ? `KOT${Math.floor(Math.random() * 1000)}` : undefined,
      loyaltyPoints: Math.floor(Math.random() * 100),
      isRegularCustomer: Math.random() > 0.6
    }
  };
}

// Initialize sample data
export function initializeSampleMultiPlatformOrders(): void {
  const manager = multiPlatformOrderManager;
  
  // Clear existing orders
  localStorage.removeItem('multiPlatformOrders');
  localStorage.removeItem('orderNotifications');
  
  // Generate sample orders
  const sampleOrders: MultiPlatformOrder[] = [];
  
  // Generate 8-12 orders for own app
  for (let i = 0; i < Math.floor(Math.random() * 5) + 8; i++) {
    sampleOrders.push(generateOwnAppOrder());
  }
  
  // Sort by order time (newest first)
  sampleOrders.sort((a, b) => 
    new Date(b.timestamps.orderPlaced).getTime() - new Date(a.timestamps.orderPlaced).getTime()
  );
  
  // Add orders to manager
  sampleOrders.forEach(order => {
    manager.addOrder(order);
  });
  
  console.log(`Generated ${sampleOrders.length} sample multi-platform orders`);
}

// Clear sample data
export function clearSampleMultiPlatformOrders(): void {
  localStorage.removeItem('multiPlatformOrders');
  localStorage.removeItem('orderNotifications');
  localStorage.removeItem('platformConfigs');
  console.log('Cleared all multi-platform order data');
}
