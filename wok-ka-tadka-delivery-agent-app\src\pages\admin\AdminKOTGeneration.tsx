import { useState, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  FileText,
  CheckCircle,
  Clock,
  Printer,
  AlertCircle,
  Utensils,
  User,
  Phone,
  Leaf,
  Plus,
  Minus,
  Calculator
} from "lucide-react";
import { kotStorage, KOTItem } from "@/utils/kotStorage";
import { Logo } from "@/components/ui/logo";
import CustomerSelectionDialog from "@/components/CustomerSelectionDialog";
import PaymentMethodDialog, { PaymentMethod } from "@/components/PaymentMethodDialog";
import { Customer, customerManager } from "@/utils/customerStorage";
import { gstSettingsManager } from "@/utils/gstSettings";
import PrintService from "@/services/printService";

const AdminKOTGeneration = () => {
  const { tableId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const {
    cart: initialCart = [],
    customerName = "",
    customerPhone = "",
    specialInstructions: initialInstructions = "",
    existingKOT = null,
    addToExisting = false,
    viewMode = false
  } = location.state || {};

  const [cart, setCart] = useState(initialCart);
  const [specialInstructions, setSpecialInstructions] = useState(initialInstructions);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentKOT, setCurrentKOT] = useState<any>(null);
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showPaymentMethodDialog, setShowPaymentMethodDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('cash');
  const [isPrintingBill, setIsPrintingBill] = useState(false);
  const [includeGST, setIncludeGST] = useState(true);
  const [gstSettings, setGSTSettings] = useState(gstSettingsManager.getGSTSettings());

  // Load KOT data when in view mode
  useEffect(() => {
    if (viewMode && tableId && !existingKOT) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (activeKOT) {
        setCurrentKOT(activeKOT);
      }
    } else if (existingKOT) {
      setCurrentKOT(existingKOT);
    }
  }, [viewMode, tableId, existingKOT]);

  // Listen for GST settings changes
  useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent) => {
      setGSTSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);

    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    };
  }, []);

  // Calculate totals
  const newItemsTotal = cart.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
  const existingTotal = existingKOT ? existingKOT.totalAmount : 0;
  const grandTotal = newItemsTotal + existingTotal;

  // Use currentKOT if available, otherwise use existingKOT
  const displayKOT = currentKOT || existingKOT;

  // Functions to handle quantity changes
  const updateItemQuantity = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return; // Don't allow quantity less than 1

    const updatedCart = [...cart];
    updatedCart[index] = { ...updatedCart[index], quantity: newQuantity };
    setCart(updatedCart);
  };

  const removeItem = (index: number) => {
    const updatedCart = cart.filter((_, i) => i !== index);
    setCart(updatedCart);
  };

  // Handle print bill button click
  const handlePrintBill = () => {
    setShowCustomerDialog(true);
  };

  // Handle customer selection for bill printing
  const handleCustomerSelection = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setShowCustomerDialog(false);
    setShowPaymentMethodDialog(true);
  };

  // Handle payment method selection
  const handlePaymentMethodSelection = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setShowPaymentMethodDialog(false);
    printBill(selectedCustomer, paymentMethod);
  };

  // Print bill with customer details and payment method
  const printBill = async (customer: Customer | null, paymentMethod: PaymentMethod = 'cash') => {
    if (!tableId) return;

    setIsPrintingBill(true);

    try {
      const kotToPrint = displayKOT;
      if (!kotToPrint) {
        toast({
          title: "No KOT Found",
          description: "No KOT data available to generate bill.",
          variant: "destructive"
        });
        return;
      }

      // Add customer order to records if customer is selected
      if (customer) {
        customerManager.addCustomerOrder({
          customerId: customer.id,
          orderId: kotToPrint.kotNumber,
          tableId: tableId,
          kotNumber: kotToPrint.kotNumber,
          amount: kotToPrint.totalAmount,
          items: kotToPrint.items.map((item: any) => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price
          }))
        });
      }

      // Use unified print service
      const success = await PrintService.printBill(kotToPrint, customer, {
        includeGST,
        paymentMethod
      });

      if (success) {
        toast({
          title: "Bill Printed Successfully!",
          description: customer
            ? `Bill printed for ${customer.name} - Table ${tableId}`
            : `Bill printed for Table ${tableId}`,
        });
      } else {
        toast({
          title: "Print Error",
          description: "Failed to print bill. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Print Error",
        description: "Failed to print bill. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsPrintingBill(false);
    }
  };

  // Generate bill content for printing
  const generateBillContent = (kot: any, customer: Customer | null, includeGST: boolean): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');
    const gstSettings = gstSettingsManager.getGSTSettings();

    // Calculate totals
    const subtotal = kot.totalAmount;
    const cgstAmount = includeGST ? (subtotal * gstSettings.cgstRate) / 100 : 0;
    const sgstAmount = includeGST ? (subtotal * gstSettings.sgstRate) / 100 : 0;
    const totalGSTAmount = cgstAmount + sgstAmount;
    const grandTotal = subtotal + totalGSTAmount;

    const itemsSection = kot.items.map((item: any, index: number) =>
      `<tr>
        <td style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">${index + 1}</td>
        <td style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">${item.name} ${item.veg ? '🟢' : '🔴'}</td>
        <td style="text-align: center; padding: 8px; border-bottom: 1px solid #ddd;">${item.quantity}</td>
        <td style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd;">₹${item.price}</td>
        <td style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd;">₹${item.price * item.quantity}</td>
      </tr>`
    ).join('');

    const customerSection = customer ?
      `<div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
        <h3 style="margin: 0 0 10px 0; color: #333;">Customer Details:</h3>
        <p style="margin: 5px 0;"><strong>Name:</strong> ${customer.name}</p>
        <p style="margin: 5px 0;"><strong>Phone:</strong> ${customer.phone}</p>
        ${customer.email ? `<p style="margin: 5px 0;"><strong>Email:</strong> ${customer.email}</p>` : ''}
        ${customer.address ? `<p style="margin: 5px 0;"><strong>Address:</strong> ${customer.address}</p>` : ''}
      </div>` : '';

    const gstSection = includeGST ?
      `<tr>
        <td colspan="4" style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">CGST (${gstSettings.cgstRate}%):</td>
        <td style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">₹${cgstAmount.toFixed(2)}</td>
      </tr>
      <tr>
        <td colspan="4" style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">SGST (${gstSettings.sgstRate}%):</td>
        <td style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">₹${sgstAmount.toFixed(2)}</td>
      </tr>` : '';

    return `<!DOCTYPE html>
<html>
<head>
  <title>Bill - Table ${tableId}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.4; }
    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
    .restaurant-name { font-size: 28px; font-weight: bold; color: #333; margin-bottom: 5px; }
    .restaurant-tagline { font-size: 14px; color: #666; margin-bottom: 10px; }
    .bill-info { margin: 20px 0; display: flex; justify-content: space-between; }
    .bill-info div { flex: 1; }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f8f9fa; font-weight: bold; }
    .total-row { background-color: #f8f9fa; font-weight: bold; font-size: 16px; }
    .grand-total { background-color: #e8f5e8; font-weight: bold; font-size: 18px; }
    .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; }
    @media print { body { margin: 0; } }
  </style>
</head>
<body>
  <div class="header">
    <div class="restaurant-name">🥢 Wok Ka Tadka</div>
    <div class="restaurant-tagline">Mumbai Style Chinese & Indian</div>
    <div style="margin-top: 15px; font-size: 18px; font-weight: bold; color: #d63384;">CUSTOMER BILL</div>
  </div>

  <div class="bill-info">
    <div>
      <strong>Bill No:</strong> ${kot.kotNumber}<br>
      <strong>Table:</strong> ${tableId}<br>
      <strong>Date:</strong> ${currentDate}<br>
      <strong>Time:</strong> ${currentTime}
    </div>
    <div style="text-align: right;">
      <strong>Order Started:</strong> ${new Date(kot.createdAt).toLocaleString('en-IN')}<br>
      <strong>Items:</strong> ${kot.items.length}<br>
      <strong>Status:</strong> ${kot.status === 'completed' ? 'Completed' : 'Active'}
    </div>
  </div>

  ${customerSection}

  <table>
    <thead>
      <tr>
        <th style="width: 8%;">Sr.</th>
        <th style="width: 40%;">Item</th>
        <th style="width: 12%; text-align: center;">Qty</th>
        <th style="width: 20%; text-align: right;">Rate</th>
        <th style="width: 20%; text-align: right;">Amount</th>
      </tr>
    </thead>
    <tbody>
      ${itemsSection}
      <tr class="total-row">
        <td colspan="4" style="text-align: right; padding: 12px;">Subtotal:</td>
        <td style="text-align: right; padding: 12px;">₹${subtotal}</td>
      </tr>
      ${gstSection}
      <tr class="grand-total">
        <td colspan="4" style="text-align: right; padding: 15px; font-size: 18px;">GRAND TOTAL:</td>
        <td style="text-align: right; padding: 15px; font-size: 18px;">₹${grandTotal.toFixed(2)}</td>
      </tr>
    </tbody>
  </table>

  <div class="footer">
    <p><strong>Thank you for dining with us!</strong></p>
    <p style="font-size: 12px; color: #666;">This is a computer generated bill</p>
    ${includeGST ? `<p style="font-size: 12px; color: #666;">GST included as per government regulations</p>` : ''}
  </div>
</body>
</html>`;
  };

  // Generate KOT content for printing
  const generateKOTContent = (kot: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    const itemsSection = kot.items.map((item: any) =>
      `<tr>
        <td>${item.name} ${item.veg ? '🟢' : '🔴'}</td>
        <td>${item.quantity}</td>
        <td>-</td>
      </tr>`
    ).join('');

    const instructionsSection = kot.specialInstructions ?
      `<div style="margin: 20px 0;">
        <strong>Special Instructions:</strong> ${kot.specialInstructions}
      </div>` : '';

    return `<!DOCTYPE html>
<html>
<head>
  <title>KOT - Table ${tableId}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { text-align: center; margin-bottom: 20px; }
    .restaurant-name { font-size: 24px; font-weight: bold; }
    .kot-info { margin: 20px 0; }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    @media print { body { margin: 0; } }
  </style>
</head>
<body>
  <div class="header">
    <div class="restaurant-name">🥢 Wok Ka Tadka</div>
    <div>Mumbai Style Chinese & Indian</div>
    <div style="margin-top: 10px; font-size: 16px; font-weight: bold;">KITCHEN ORDER TICKET</div>
  </div>

  <div class="kot-info">
    <strong>KOT No:</strong> ${kot.kotNumber}<br>
    <strong>Table:</strong> ${tableId}<br>
    <strong>Date:</strong> ${currentDate}<br>
    <strong>Time:</strong> ${currentTime}
  </div>

  <table>
    <thead>
      <tr>
        <th>Item</th>
        <th>Qty</th>
        <th>Special Notes</th>
      </tr>
    </thead>
    <tbody>
      ${itemsSection}
    </tbody>
  </table>

  ${instructionsSection}

  <div style="text-align: center; margin-top: 20px;">
    <p><strong>Total Items: ${kot.items.reduce((sum: number, item: any) => sum + item.quantity, 0)}</strong></p>
    <p>Please prepare items as per order</p>
  </div>
</body>
</html>`;
  };

  const handleGenerateKOT = async () => {
    if (!tableId) return;

    setIsGenerating(true);

    try {
      const kotItems: KOTItem[] = cart.map((item: any) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        veg: item.veg,
        spicy: item.spicy || 0,
        image: item.image || "/img/app.png",
        addedAt: new Date().toISOString()
      }));

      let result: any;

      if (addToExisting && existingKOT) {
        // Add items to existing KOT and print only the new items
        result = kotStorage.addItemsToKOT(existingKOT.kotNumber, kotItems, specialInstructions);

        if (result) {
          // Generate KOT content for only the NEW items being added
          const newItemsKOT = {
            ...result,
            items: kotItems, // Only the new items
            kotNumber: `${result.kotNumber}-ADD${result.versions.length}`, // Add suffix to show it's an addition
            specialInstructions: specialInstructions || `Additional items for KOT #${existingKOT.kotNumber}`
          };

          // Print the KOT for new items
          const kotContent = generateKOTContent(newItemsKOT);
          const printWindow = window.open('', '_blank');
          if (printWindow) {
            printWindow.document.write(kotContent);
            printWindow.document.close();
            printWindow.print();
          }

          toast({
            title: "Items Added & KOT Printed!",
            description: `${kotItems.length} items added to KOT #${existingKOT.kotNumber} and printed for kitchen`,
          });
        }
      } else {
        // Create new KOT
        result = kotStorage.createKOT(tableId, kotItems, specialInstructions);

        if (result) {
          // Print the new KOT using unified print service
          const success = await PrintService.printKOT(result);

          if (success) {
            toast({
              title: "KOT Generated & Printed!",
              description: `KOT #${result.kotNumber} created and sent to kitchen for Table ${tableId}`,
            });
          } else {
            toast({
              title: "KOT Generated",
              description: `KOT #${result.kotNumber} created but printing failed. Please try printing manually.`,
              variant: "destructive"
            });
          }
        }
      }

      if (!result) {
        throw new Error("Failed to process KOT");
      }

      // Navigate back to admin table management after a short delay
      setTimeout(() => {
        navigate("/admin/tables");
      }, 2000);

    } catch (error) {
      console.error("Error generating KOT:", error);
      toast({
        title: "Error",
        description: "Failed to generate KOT. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle case when in view mode but no KOT data available
  if (viewMode && !displayKOT) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Active KOT Found</h2>
            <p className="text-gray-600 mb-4">No active KOT found for Table {tableId}</p>
            <Button onClick={() => navigate(`/admin/take-order?table=${tableId}&dine-in=true`)}>
              Back to Order Taking
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle case when cart becomes empty after removing items
  if (!viewMode && cart.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Items in Cart</h2>
            <p className="text-gray-600 mb-4">Please add items to cart before generating KOT</p>
            <Button onClick={() => navigate(`/admin/take-order?table=${tableId}&dine-in=true`)}>
              Back to Menu
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (viewMode && displayKOT) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/admin/take-order?table=${tableId}&dine-in=true`)}
                  className="flex items-center gap-1 sm:gap-2 shrink-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span className="hidden sm:inline">Back</span>
                </Button>
                <Logo className="h-6 sm:h-8 shrink-0" />
                <div className="min-w-0 flex-1">
                  <h1 className="text-base sm:text-lg font-bold text-gray-900 truncate">KOT Details</h1>
                  <p className="text-xs sm:text-sm text-gray-600 truncate">Table {tableId} • KOT #{displayKOT.kotNumber}</p>
                </div>
              </div>
              <Badge
                variant={displayKOT.status === 'completed' ? 'default' : 'secondary'}
                className={`${displayKOT.status === 'completed' ? 'bg-green-600' : 'bg-blue-600'} text-xs sm:text-sm shrink-0`}
              >
                {displayKOT.status === 'completed' ? 'Completed' : 'Active'}
              </Badge>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto p-3 sm:p-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                Complete Order History - Table {tableId}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Order Timeline */}
              <div className="space-y-4">
                {displayKOT.versions.map((version: any, versionIndex: number) => (
                  <div key={versionIndex} className="bg-gray-50 rounded-lg p-3 sm:p-4 border-l-4 border-blue-400">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-semibold text-gray-800 flex items-center gap-2 text-sm sm:text-base">
                        <Clock className="h-4 w-4 text-blue-600" />
                        Order #{versionIndex + 1}
                        {versionIndex === 0 && <Badge className="bg-green-100 text-green-700 text-xs">Initial</Badge>}
                        {versionIndex > 0 && <Badge className="bg-orange-100 text-orange-700 text-xs">Added</Badge>}
                      </h5>
                      <span className="text-xs text-gray-500 shrink-0">
                        {new Date(version.addedAt).toLocaleString()}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      {(version.addedItems || version.items || []).map((item: KOTItem, itemIndex: number) => (
                        <div key={itemIndex} className="flex justify-between items-center py-2 bg-white rounded px-3">
                          <div className="flex items-center gap-2 min-w-0 flex-1 pr-3">
                            <span className="text-gray-800 font-medium text-sm sm:text-base truncate">{item.name}</span>
                            {item.veg ? (
                              <div className="w-3 h-3 border border-green-500 rounded-sm flex items-center justify-center shrink-0">
                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                              </div>
                            ) : (
                              <div className="w-3 h-3 border border-red-500 rounded-sm flex items-center justify-center shrink-0">
                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                              </div>
                            )}
                            {item.spicy > 0 && (
                              <div className="flex shrink-0">
                                {Array.from({ length: item.spicy }).map((_, i) => (
                                  <span key={i} className="text-red-500 text-xs">🌶️</span>
                                ))}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-gray-700 shrink-0">
                            <span className="text-xs sm:text-sm">₹{item.price} × {item.quantity}</span>
                            <span className="font-semibold text-sm sm:text-base">₹{item.price * item.quantity}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    {version.specialInstructions && (
                      <div className="mt-3 p-2 bg-yellow-50 rounded text-xs text-yellow-800">
                        <strong>Instructions:</strong> {version.specialInstructions}
                      </div>
                    )}
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center text-xl font-bold text-gray-900">
                  <span>Grand Total ({displayKOT.items.length} items):</span>
                  <span>₹{displayKOT.totalAmount}</span>
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  Order started: {new Date(displayKOT.createdAt).toLocaleString()}
                  {displayKOT.completedAt && (
                    <span className="ml-2">• Completed: {new Date(displayKOT.completedAt).toLocaleString()}</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* GST Toggle */}
          <Card className="mt-6">
            <CardContent className="p-4">
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Calculator className="h-5 w-5 text-yellow-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">GST (CGST + SGST)</h3>
                      <p className="text-sm text-gray-600">Include {gstSettings.totalGSTRate}% GST in this bill</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${includeGST ? 'text-green-600' : 'text-gray-500'}`}>
                      {includeGST ? 'With GST' : 'Without GST'}
                    </span>
                    <Switch
                      checked={includeGST}
                      onCheckedChange={setIncludeGST}
                      className="data-[state=checked]:bg-green-600"
                    />
                  </div>
                </div>
                {!includeGST && (
                  <div className="mt-3 p-3 bg-orange-50 rounded border border-orange-200">
                    <p className="text-sm text-orange-700">
                      <strong>Note:</strong> GST will be excluded from this bill. This may be required for certain customer categories or business scenarios.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="mt-6 flex flex-col sm:flex-row gap-3">
            <Button
              variant="default"
              size="lg"
              onClick={handlePrintBill}
              disabled={isPrintingBill}
              className="flex-1 h-12 sm:h-14 text-sm sm:text-base"
            >
              {isPrintingBill ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Printing...
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4 mr-2" />
                  Print Bill
                </>
              )}
            </Button>

            {displayKOT.status === 'active' && (
              <Button
                variant="default"
                size="lg"
                className="flex-1 h-12 sm:h-14 bg-green-600 hover:bg-green-700 text-sm sm:text-base"
                onClick={() => {
                  kotStorage.completeKOT(displayKOT.kotNumber);
                  toast({
                    title: "Order Completed!",
                    description: `KOT #${displayKOT.kotNumber} for Table ${tableId} has been completed`,
                  });
                  setTimeout(() => {
                    navigate("/admin/dashboard");
                  }, 1500);
                }}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Complete Order
              </Button>
            )}
          </div>
        </div>

        {/* Customer Selection Dialog */}
        <CustomerSelectionDialog
          isOpen={showCustomerDialog}
          onClose={() => setShowCustomerDialog(false)}
          onCustomerSelect={handleCustomerSelection}
          title="Select Customer for Bill"
          description="Choose a customer for this bill or skip to print without customer details"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(`/admin/take-order?table=${tableId}&dine-in=true`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <Logo className="h-8" />
            <div>
              <h1 className="text-lg font-bold text-gray-900">
                {addToExisting ? "Add Items to KOT" : "Generate KOT"}
              </h1>
              <p className="text-sm text-gray-600">Table {tableId}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">Customer Name</Label>
                <p className="text-gray-900 font-medium">{customerName || "Walk-in Customer"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">Phone Number</Label>
                <p className="text-gray-900 font-medium">{customerPhone || "Not provided"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Existing KOT Items (when adding to existing) */}
        {addToExisting && existingKOT && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Previously Ordered Items
                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                  KOT #{existingKOT.kotNumber}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {existingKOT.items.map((item: KOTItem, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 bg-gray-50 rounded px-3">
                    <div className="flex items-center gap-2">
                      <span className="text-gray-800 font-medium">{item.name}</span>
                      {item.veg ? (
                        <div className="w-3 h-3 border border-green-500 rounded-sm flex items-center justify-center">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        </div>
                      ) : (
                        <div className="w-3 h-3 border border-red-500 rounded-sm flex items-center justify-center">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-gray-700">
                      <span className="text-sm">₹{item.price} × {item.quantity}</span>
                      <span className="font-semibold">₹{item.price * item.quantity}</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between font-semibold text-gray-900">
                <span>Previous Order Total:</span>
                <span>₹{existingKOT.totalAmount}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* New Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Utensils className="h-5 w-5" />
              {addToExisting ? "New Items to Add" : "Order Items"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {cart.map((item: any, index: number) => (
                <div key={index} className="group relative bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300 p-4 mb-3">
                  {/* Item Image with Professional Styling */}
                  <div className="relative flex-shrink-0">
                    <div className="w-20 h-20 rounded-xl overflow-hidden border-2 border-gray-200 shadow-md">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        onError={(e) => {
                          e.currentTarget.src = "/img/app.png";
                        }}
                      />
                    </div>
                    {/* Veg/Non-veg Badge */}
                    <div className="absolute -top-1 -right-1">
                      {item.veg ? (
                        <div className="w-6 h-6 bg-green-500 rounded-full border-2 border-white shadow-sm flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      ) : (
                        <div className="w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-sm flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Item Details with Professional Typography */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-bold text-gray-900 text-lg leading-tight mb-2 truncate">{item.name}</h4>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <span className="text-sm font-medium text-gray-500">Unit Price:</span>
                            <span className="text-sm font-bold text-green-600 bg-green-50 px-2 py-1 rounded-full">₹{item.price}</span>
                          </div>
                          {item.spicy > 0 && (
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-gray-500">Spice:</span>
                              <div className="flex">
                                {Array.from({ length: item.spicy }).map((_, i) => (
                                  <span key={i} className="text-red-500 text-sm">🌶️</span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Professional Quantity Controls & Actions */}
                  <div className="flex flex-col items-end gap-4">
                    {/* Modern Quantity Selector */}
                    <div className="flex items-center bg-white rounded-xl border-2 border-gray-200 shadow-sm hover:border-blue-400 transition-colors duration-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateItemQuantity(index, item.quantity - 1)}
                        disabled={item.quantity <= 1}
                        className="h-12 w-12 p-0 rounded-l-xl hover:bg-red-50 hover:text-red-600 disabled:opacity-30 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        <Minus className="h-5 w-5" />
                      </Button>
                      <div className="w-16 h-12 flex items-center justify-center bg-gradient-to-b from-blue-50 to-white border-x-2 border-gray-200">
                        <span className="font-bold text-xl text-gray-900">{item.quantity}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateItemQuantity(index, item.quantity + 1)}
                        className="h-12 w-12 p-0 rounded-r-xl hover:bg-green-50 hover:text-green-600 transition-all duration-200"
                      >
                        <Plus className="h-5 w-5" />
                      </Button>
                    </div>

                    {/* Professional Price Display */}
                    <div className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-200 shadow-sm">
                      <div className="text-xs font-medium text-gray-600 mb-1">Total Amount</div>
                      <div className="font-bold text-2xl text-blue-700">
                        ₹{item.price * item.quantity}
                      </div>
                    </div>

                    {/* Elegant Remove Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(index)}
                      className="text-red-500 hover:text-white hover:bg-red-500 text-sm px-4 py-2 rounded-lg border border-red-200 hover:border-red-500 transition-all duration-200 font-medium shadow-sm"
                    >
                      <Minus className="h-4 w-4 mr-1" />
                      Remove Item
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Professional Totals Section */}
            <div className="mt-6 pt-4 border-t-2 border-gray-200">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200 shadow-sm">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-lg font-semibold text-gray-700">
                    {addToExisting ? "New Items Total:" : "Order Total:"}
                  </span>
                  <span className="text-2xl font-bold text-blue-600">₹{newItemsTotal}</span>
                </div>
                {addToExisting && (
                  <div className="flex justify-between items-center pt-2 border-t border-blue-200">
                    <span className="text-xl font-bold text-gray-800">Grand Total:</span>
                    <span className="text-3xl font-bold text-green-600">₹{grandTotal}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Professional Special Instructions */}
        <Card className="border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <FileText className="h-5 w-5" />
              Special Instructions for Kitchen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Add any special cooking instructions, dietary requirements, or preferences..."
              value={specialInstructions}
              onChange={(e) => setSpecialInstructions(e.target.value)}
              className="min-h-[120px] border-2 border-orange-200 focus:border-orange-400 bg-white rounded-lg text-gray-800 placeholder:text-gray-500"
            />
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            size="lg"
            onClick={() => navigate(`/admin/take-order?table=${tableId}&dine-in=true`)}
            className="flex-1 h-16 sm:h-18 text-base sm:text-lg font-medium"
          >
            <ArrowLeft className="mr-2 h-5 w-5 sm:h-6 sm:w-6" />
            <span className="truncate">Back to Order</span>
          </Button>
          <Button
            variant="default"
            size="lg"
            onClick={handleGenerateKOT}
            disabled={isGenerating || cart.length === 0}
            className="flex-1 h-16 sm:h-18 text-base sm:text-lg font-medium"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 shrink-0" />
                <span className="truncate">Processing...</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 mr-2 shrink-0" />
                <span className="truncate">
                  {addToExisting ? "Print KOT (New Items)" : "Generate KOT"}
                </span>
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Customer Selection Dialog */}
      <CustomerSelectionDialog
        isOpen={showCustomerDialog}
        onClose={() => setShowCustomerDialog(false)}
        onCustomerSelect={handleCustomerSelection}
        title="Select Customer for Bill"
        description="Choose a customer for this bill or skip to print without customer details"
      />

      {/* Payment Method Selection Dialog */}
      <PaymentMethodDialog
        open={showPaymentMethodDialog}
        onClose={() => setShowPaymentMethodDialog(false)}
        onSelect={handlePaymentMethodSelection}
        title="Select Payment Method"
      />
    </div>
  );
};

export default AdminKOTGeneration;
