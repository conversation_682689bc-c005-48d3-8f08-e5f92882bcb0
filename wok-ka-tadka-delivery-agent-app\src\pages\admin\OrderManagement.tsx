import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Search,
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Phone,
  MapPin,
  Utensils,
  Package,
  RefreshCw,
  Eye,
  Edit,
  Printer
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import PrintService from "@/services/printService";
import { useToast } from "@/hooks/use-toast";

const OrderManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedType, setSelectedType] = useState<string>("all");

  // Mock orders data
  const orders = [
    {
      id: "#ORD001",
      type: "dine-in",
      table: "Table 3",
      customer: "Walk-in Customer",
      phone: "+91 98765 43210",
      items: [
        { name: "Chicken Biryani", quantity: 2, price: 300 },
        { name: "Paneer Butter Masala", quantity: 1, price: 250 },
        { name: "Garlic Naan", quantity: 3, price: 60 }
      ],
      totalAmount: 1030,
      status: "preparing",
      orderTime: "12:30 PM",
      estimatedTime: "15 mins",
      waiter: "Raj Kumar",
      specialInstructions: "Less spicy"
    },
    {
      id: "#ORD002",
      type: "delivery",
      customer: "Priya Sharma",
      phone: "+91 87654 32109",
      address: "Sector 18, Noida, UP - 201301",
      items: [
        { name: "Mutton Curry", quantity: 1, price: 400 },
        { name: "Jeera Rice", quantity: 2, price: 140 }
      ],
      totalAmount: 680,
      status: "ready",
      orderTime: "12:45 PM",
      estimatedTime: "5 mins",
      deliveryAgent: "Amit Singh",
      specialInstructions: ""
    },
    {
      id: "#ORD003",
      type: "dine-in",
      table: "Table 7",
      customer: "Rohit Gupta",
      phone: "+91 76543 21098",
      items: [
        { name: "Dal Makhani", quantity: 2, price: 200 },
        { name: "Butter Naan", quantity: 4, price: 50 },
        { name: "Lassi", quantity: 2, price: 80 }
      ],
      totalAmount: 740,
      status: "completed",
      orderTime: "11:15 AM",
      completedTime: "11:45 AM",
      waiter: "Sunita Devi",
      specialInstructions: "Extra butter on naan"
    },
    {
      id: "#ORD004",
      type: "takeaway",
      customer: "Anjali Singh",
      phone: "+91 65432 10987",
      items: [
        { name: "Chicken Tikka", quantity: 1, price: 350 },
        { name: "Mint Chutney", quantity: 2, price: 30 }
      ],
      totalAmount: 410,
      status: "cancelled",
      orderTime: "1:20 PM",
      cancelReason: "Customer cancelled",
      specialInstructions: ""
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "preparing": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "ready": return "bg-green-100 text-green-800 border-green-200";
      case "completed": return "bg-blue-100 text-blue-800 border-blue-200";
      case "cancelled": return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "preparing": return <Clock className="h-4 w-4" />;
      case "ready": return <CheckCircle className="h-4 w-4" />;
      case "completed": return <CheckCircle className="h-4 w-4" />;
      case "cancelled": return <XCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "dine-in": return <Utensils className="h-4 w-4" />;
      case "delivery": return <Package className="h-4 w-4" />;
      case "takeaway": return <Package className="h-4 w-4" />;
      default: return <Utensils className="h-4 w-4" />;
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === "all" || order.status === selectedStatus;
    const matchesType = selectedType === "all" || order.type === selectedType;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const orderStats = {
    total: orders.length,
    preparing: orders.filter(o => o.status === "preparing").length,
    ready: orders.filter(o => o.status === "ready").length,
    completed: orders.filter(o => o.status === "completed").length,
    cancelled: orders.filter(o => o.status === "cancelled").length
  };

  const handleStatusUpdate = (orderId: string, newStatus: string) => {
    // Mock status update
    alert(`Order ${orderId} status updated to ${newStatus}`);
  };

  const handleViewOrder = (orderId: string) => {
    // Encode the order ID to handle special characters like #
    const encodedOrderId = encodeURIComponent(orderId);
    navigate(`/admin/order-details/${encodedOrderId}`);
  };

  // Generate HTML content for KOT printing
  const generateKOTHTML = (order: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>KOT - ${order.id}</title>
      <style>
        body {
          font-family: 'Courier New', monospace;
          margin: 0;
          padding: 20px;
          font-size: 12px;
          line-height: 1.4;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #000;
          padding-bottom: 10px;
          margin-bottom: 15px;
        }
        .restaurant-name {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .kot-title {
          font-size: 16px;
          font-weight: bold;
          margin: 10px 0;
        }
        .order-info {
          margin-bottom: 15px;
        }
        .order-info div {
          margin-bottom: 3px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 15px;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 5px;
          text-align: left;
        }
        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        .footer {
          border-top: 1px solid #000;
          padding-top: 10px;
          text-align: center;
          font-size: 10px;
        }
        .special-instructions {
          background-color: #fffacd;
          border: 1px solid #ddd;
          padding: 8px;
          margin: 10px 0;
          border-radius: 3px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="restaurant-name">WOK KA TADKA</div>
        <div class="kot-title">KITCHEN ORDER TICKET (KOT)</div>
      </div>

      <div class="order-info">
        <div><strong>Order ID:</strong> ${order.id}</div>
        <div><strong>Table:</strong> ${order.table}</div>
        <div><strong>Order Type:</strong> ${order.type}</div>
        <div><strong>Waiter:</strong> ${order.waiter}</div>
        <div><strong>Order Time:</strong> ${order.orderTime}</div>
        <div><strong>Date:</strong> ${currentDate}</div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Qty</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          ${order.items.map((item: any) => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>-</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      ${order.specialInstructions ? `
        <div class="special-instructions">
          <strong>Special Instructions:</strong><br>
          ${order.specialInstructions}
        </div>
      ` : ''}

      <div class="footer">
        <p>Generated on ${currentDate} at ${currentTime}</p>
        <p>Please prepare items as per order specifications</p>
      </div>
    </body>
    </html>`;
  };

  const handlePrintKOT = async (orderId: string) => {
    try {
      // Find the order data
      const order = orders.find(o => o.id === orderId);
      if (!order) {
        toast({
          title: "Error",
          description: "Order not found",
          variant: "destructive"
        });
        return;
      }

      // Convert order to KOT format for unified print service
      const kotData = {
        id: order.id,
        kotNumber: order.id,
        tableId: order.tableNumber || 'N/A',
        items: order.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          specialInstructions: item.specialInstructions || ''
        })),
        totalAmount: order.totalAmount,
        createdAt: new Date().toISOString(),
        status: 'active'
      };

      // Print the KOT using unified print service
      const success = await PrintService.printKOT(kotData);

      if (success) {
        toast({
          title: "KOT Printed Successfully!",
          description: `KOT for order ${order.id} printed successfully`,
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      console.error('Failed to print KOT:', error);
      toast({
        title: "Print Error",
        description: "Failed to print KOT. Please try again.",
        variant: "destructive"
      });

      // Fallback to browser printing
      const order = orders.find(o => o.id === orderId);
      if (order) {
        const kotHTML = generateKOTHTML(order);
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          printWindow.document.write(kotHTML);
          printWindow.document.close();
          printWindow.onload = () => {
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
            }, 500);
          };
        }
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-3 sm:p-4">
          <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Order Management</h1>
              <p className="text-xs sm:text-sm text-gray-500 truncate">Track and manage all orders</p>
            </div>
          </div>
          <div className="flex items-center gap-1 sm:gap-2 shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/admin/take-order")}
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
            >
              <Package className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Take Order</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600 shrink-0"
            >
              <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-3 sm:p-6 space-y-4 sm:space-y-6">
        {/* Order Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{orderStats.total}</p>
              <p className="text-xs sm:text-sm text-blue-100">Total Orders</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{orderStats.preparing}</p>
              <p className="text-xs sm:text-sm text-yellow-100">Preparing</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{orderStats.ready}</p>
              <p className="text-xs sm:text-sm text-green-100">Ready</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{orderStats.completed}</p>
              <p className="text-xs sm:text-sm text-blue-100">Completed</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{orderStats.cancelled}</p>
              <p className="text-xs sm:text-sm text-red-100">Cancelled</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by order ID or customer name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="preparing">Preparing</option>
                  <option value="ready">Ready</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                >
                  <option value="all">All Types</option>
                  <option value="dine-in">Dine-in</option>
                  <option value="delivery">Delivery</option>
                  <option value="takeaway">Takeaway</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order) => (
            <Card key={order.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-4 gap-3 sm:gap-4">
                  <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                    <div className="flex items-center gap-2 shrink-0">
                      {getTypeIcon(order.type)}
                      <span className="font-bold text-base sm:text-lg">{order.id}</span>
                    </div>
                    <Badge className={`${getStatusColor(order.status)} flex items-center gap-1 text-xs`}>
                      {getStatusIcon(order.status)}
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 shrink-0 justify-end sm:justify-start">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewOrder(order.id)}
                      className="flex items-center gap-1 text-xs sm:text-sm h-8 sm:h-9"
                    >
                      <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">View</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePrintKOT(order.id)}
                      className="flex items-center gap-1 text-xs sm:text-sm h-8 sm:h-9"
                    >
                      <Printer className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Print</span>
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <div>
                    <p className="text-xs sm:text-sm text-gray-500">Customer</p>
                    <p className="font-medium text-sm sm:text-base truncate">{order.customer}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Phone className="h-3 w-3 text-gray-400 shrink-0" />
                      <span className="text-xs text-gray-500 truncate">{order.phone}</span>
                    </div>
                  </div>

                  <div>
                    <p className="text-xs sm:text-sm text-gray-500">
                      {order.type === "dine-in" ? "Table" : order.type === "delivery" ? "Address" : "Pickup"}
                    </p>
                    <p className="font-medium text-sm sm:text-base truncate">
                      {order.table || order.address || "Counter Pickup"}
                    </p>
                    {order.waiter && (
                      <p className="text-xs text-gray-500 truncate">Waiter: {order.waiter}</p>
                    )}
                    {order.deliveryAgent && (
                      <p className="text-xs text-gray-500 truncate">Agent: {order.deliveryAgent}</p>
                    )}
                  </div>

                  <div className="sm:col-span-2 lg:col-span-1">
                    <p className="text-xs sm:text-sm text-gray-500">Order Time</p>
                    <p className="font-medium text-sm sm:text-base">{order.orderTime}</p>
                    {order.estimatedTime && (
                      <p className="text-xs text-green-600">ETA: {order.estimatedTime}</p>
                    )}
                    {order.completedTime && (
                      <p className="text-xs text-blue-600">Completed: {order.completedTime}</p>
                    )}
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-xs sm:text-sm text-gray-500 mb-2">Items ({order.items.length})</p>
                  <div className="space-y-1">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex justify-between text-xs sm:text-sm">
                        <span className="truncate pr-2">{item.quantity}x {item.name}</span>
                        <span className="shrink-0">₹{(item.quantity * item.price).toLocaleString()}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center justify-between pt-4 border-t gap-3 sm:gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="text-base sm:text-lg font-bold">Total: ₹{order.totalAmount.toLocaleString()}</p>
                    {order.specialInstructions && (
                      <p className="text-xs text-orange-600 truncate">Note: {order.specialInstructions}</p>
                    )}
                    {order.cancelReason && (
                      <p className="text-xs text-red-600 truncate">Cancelled: {order.cancelReason}</p>
                    )}
                  </div>

                  {order.status === "preparing" && (
                    <div className="flex gap-2 shrink-0 justify-end sm:justify-start">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusUpdate(order.id, "ready")}
                        className="text-green-600 border-green-600 hover:bg-green-50 text-xs sm:text-sm h-8 sm:h-9"
                      >
                        <span className="hidden sm:inline">Mark Ready</span>
                        <span className="sm:hidden">Ready</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusUpdate(order.id, "cancelled")}
                        className="text-red-600 border-red-600 hover:bg-red-50 text-xs sm:text-sm h-8 sm:h-9"
                      >
                        <span className="hidden sm:inline">Cancel</span>
                        <span className="sm:hidden">Cancel</span>
                      </Button>
                    </div>
                  )}

                  {order.status === "ready" && (
                    <div className="shrink-0 flex justify-end sm:justify-start">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusUpdate(order.id, "completed")}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50 text-xs sm:text-sm h-8 sm:h-9"
                      >
                        <span className="hidden sm:inline">Mark Completed</span>
                        <span className="sm:hidden">Completed</span>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredOrders.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No orders found matching your criteria</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default OrderManagement;
