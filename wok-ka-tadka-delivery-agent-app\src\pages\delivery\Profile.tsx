
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  User,
  Phone,
  MapPin,
  Clock,
  Star,
  Edit,
  Settings
} from "lucide-react";

const Profile = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isOnline, setIsOnline] = useState(true);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState({
    name: "",
    phone: "",
    employeeId: ""
  });

  // Get current staff info from localStorage
  const currentStaff = JSON.parse(localStorage.getItem('currentStaff') || '{}');

  const profileData = {
    name: currentStaff.name || "Staff Member",
    phone: currentStaff.phone || "+91 98765 43210",
    employeeId: currentStaff.id || "WKT001",
    joinDate: currentStaff.joinDate || "15 Jan 2024",
    totalDeliveries: currentStaff.totalDeliveries || 142,
    rating: currentStaff.rating || 4.8,
    onTimeDeliveries: currentStaff.onTimeDeliveries || 95
  };

  const handleEditProfile = () => {
    // Initialize form with current data
    setEditFormData({
      name: profileData.name,
      phone: profileData.phone,
      employeeId: profileData.employeeId
    });
    setShowEditModal(true);
  };

  const handleSaveProfile = () => {
    // Get current staff data
    const currentStaff = JSON.parse(localStorage.getItem('currentStaff') || '{}');

    // Update with new data
    const updatedStaff = {
      ...currentStaff,
      name: editFormData.name,
      phone: editFormData.phone,
      id: editFormData.employeeId
    };

    // Save back to localStorage
    localStorage.setItem('currentStaff', JSON.stringify(updatedStaff));

    // Show success message
    toast({
      title: "Profile Updated",
      description: "Your profile information has been successfully updated.",
    });

    // Close modal and refresh page to show updated data
    setShowEditModal(false);
    window.location.reload();
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">My Profile</h1>
              <p className="text-white/80 text-sm">Account information</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/20"
          >
            <Settings className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Profile Card */}
        <Card className="shadow-sm border-0 bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="bg-primary/10 p-4 rounded-full">
                  <User className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{profileData.name}</h2>
                  <p className="text-gray-500">Delivery Agent</p>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm text-gray-600">
                      {isOnline ? "Online" : "Offline"}
                    </span>
                  </div>
                </div>
              </div>
              <Button variant="outline" size="sm" onClick={handleEditProfile}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-primary" />
                <span>{profileData.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-primary" />
                <span>ID: {profileData.employeeId}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-primary" />
                <span>Joined {profileData.joinDate}</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-primary" />
                <span>{profileData.rating} ★ Rating</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-3 gap-3">
          <Card className="shadow-sm border-0 bg-white">
            <CardContent className="p-4 text-center">
              <div className="bg-primary/10 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                <MapPin className="h-6 w-6 text-primary" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{profileData.totalDeliveries}</p>
              <p className="text-xs text-gray-500">Total Deliveries</p>
            </CardContent>
          </Card>

          <Card className="shadow-sm border-0 bg-white">
            <CardContent className="p-4 text-center">
              <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                <Clock className="h-6 w-6 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{profileData.onTimeDeliveries}%</p>
              <p className="text-xs text-gray-500">On-Time</p>
            </CardContent>
          </Card>

          <Card className="shadow-sm border-0 bg-white">
            <CardContent className="p-4 text-center">
              <div className="bg-yellow-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{profileData.rating}</p>
              <p className="text-xs text-gray-500">Rating</p>
            </CardContent>
          </Card>
        </div>

        {/* Status Toggle */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader>
            <CardTitle className="text-lg">Availability Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">
                  {isOnline ? "Available for Deliveries" : "Currently Offline"}
                </p>
                <p className="text-sm text-gray-500">
                  Toggle to control delivery assignments
                </p>
              </div>
              <Button
                variant={isOnline ? "success" : "outline"}
                onClick={() => setIsOnline(!isOnline)}
              >
                {isOnline ? "Go Offline" : "Go Online"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit Profile Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-primary" />
              Edit Profile
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={editFormData.name}
                onChange={(e) => setEditFormData({...editFormData, name: e.target.value})}
                placeholder="Enter your full name"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={editFormData.phone}
                onChange={(e) => setEditFormData({...editFormData, phone: e.target.value})}
                placeholder="Enter your phone number"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="employeeId">Employee ID</Label>
              <Input
                id="employeeId"
                value={editFormData.employeeId}
                onChange={(e) => setEditFormData({...editFormData, employeeId: e.target.value})}
                placeholder="Enter your employee ID"
                className="mt-1"
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setShowEditModal(false)}
              >
                Cancel
              </Button>
              <Button
                className="flex-1"
                onClick={handleSaveProfile}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Profile;
