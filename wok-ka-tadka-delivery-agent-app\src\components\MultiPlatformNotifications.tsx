import React, { useState, useEffect } from 'react';
import { Bell, X, Check, AlertCircle, Package, Clock, ArrowLeft } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useNavigate } from 'react-router-dom';
import {
  OrderNotification,
  multiPlatformOrderManager
} from '@/utils/multiPlatformOrders';

interface MultiPlatformNotificationsProps {
  onNotificationClick?: (orderId: string) => void;
  currentPage?: string; // Track current page to determine navigation behavior
}

const MultiPlatformNotifications: React.FC<MultiPlatformNotificationsProps> = ({
  onNotificationClick,
  currentPage = 'dashboard'
}) => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<OrderNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    loadNotifications();
    
    // Set up periodic refresh
    const interval = setInterval(loadNotifications, 10000); // Every 10 seconds
    
    return () => clearInterval(interval);
  }, []);

  const loadNotifications = () => {
    const allNotifications = multiPlatformOrderManager.getNotifications();
    const unreadCount = multiPlatformOrderManager.getUnreadNotificationsCount();
    
    setNotifications(allNotifications);
    setUnreadCount(unreadCount);
  };

  const handleNotificationClick = (notification: OrderNotification) => {
    // Mark as read
    multiPlatformOrderManager.markNotificationAsRead(notification.id);
    loadNotifications();

    // Close the sheet first
    setIsOpen(false);

    // Navigate based on current page context
    if (onNotificationClick) {
      setTimeout(() => {
        // If we're already on multi-platform pages, go back to dashboard
        if (currentPage === 'multi-platform-orders' || currentPage === 'multi-platform-order-details') {
          // Navigate to dashboard instead of order details
          window.location.href = '/admin/dashboard';
        } else {
          // Normal behavior - navigate to order details
          onNotificationClick(notification.orderId);
        }
      }, 100);
    }
  };

  const handleMarkAllAsRead = () => {
    multiPlatformOrderManager.markAllNotificationsAsRead();
    loadNotifications();
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'own-app':
        return '🏪';
      default:
        return '📱';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'own-app':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new-order':
        return <Package className="h-4 w-4" />;
      case 'status-update':
        return <Clock className="h-4 w-4" />;
      case 'cancellation':
        return <X className="h-4 w-4" />;
      case 'payment-update':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-300';
    }
  };

  const formatTime = (timestamp: string) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ago`;
    } else {
      return notificationTime.toLocaleDateString();
    }
  };

  // Play notification sound for new notifications
  useEffect(() => {
    if (unreadCount > 0) {
      // You can add sound notification here
      // const audio = new Audio('/notification-sound.mp3');
      // audio.play().catch(() => {}); // Ignore errors if sound fails
    }
  }, [unreadCount]);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative text-white hover:bg-white/20 h-8 w-8 sm:h-10 sm:w-10"
        >
          <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs bg-red-500 hover:bg-red-500 flex items-center justify-center">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:w-96 z-[10002]">
        <SheetHeader className="pb-4 border-b">
          <SheetTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifications
            </div>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  className="text-xs"
                >
                  <Check className="h-3 w-3 mr-1" />
                  Mark all read
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="p-1 h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </SheetTitle>
          <SheetDescription>
            Order notifications from all platforms
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          {notifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Notifications</h3>
              <p className="text-gray-600">
                You'll see order notifications from all platforms here.
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[calc(100vh-280px)]">
              <div className="space-y-3">
                {notifications.map((notification) => (
                  <Card
                    key={notification.id}
                    className={`cursor-pointer transition-all hover:shadow-md border-l-4 ${getPriorityColor(notification.priority)} ${
                      !notification.isRead ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-lg">
                              {getPlatformIcon(notification.platform)}
                            </span>
                            <Badge className={`${getPlatformColor(notification.platform)} text-xs`}>
                              {notification.platform.toUpperCase()}
                            </Badge>
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                          <h4 className="font-medium text-gray-900 text-sm mb-1">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-gray-600 mb-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">
                              {formatTime(notification.timestamp)}
                            </span>
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                notification.priority === 'high'
                                  ? 'border-red-200 text-red-700'
                                  : notification.priority === 'medium'
                                  ? 'border-yellow-200 text-yellow-700'
                                  : 'border-green-200 text-green-700'
                              }`}
                            >
                              {notification.priority}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MultiPlatformNotifications;
