import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { 
  <PERSON>ertTriangle, 
  CheckCircle, 
  XCircle, 
  Wifi, 
  Bluetooth, 
  Usb,
  Info
} from 'lucide-react';

interface PrinterTroubleshootingProps {
  connectionType: 'usb' | 'bluetooth' | 'network';
  error?: string;
}

const PrinterTroubleshooting: React.FC<PrinterTroubleshootingProps> = ({ 
  connectionType, 
  error 
}) => {
  const getConnectionIcon = () => {
    switch (connectionType) {
      case 'usb': return <Usb className="h-4 w-4" />;
      case 'bluetooth': return <Bluetooth className="h-4 w-4" />;
      case 'network': return <Wifi className="h-4 w-4" />;
    }
  };

  const getConnectionSteps = () => {
    switch (connectionType) {
      case 'usb':
        return [
          'Connect your EC58 printer via USB cable',
          'Make sure the printer is powered on',
          'Check that the USB cable is working (try another cable if needed)',
          'Use Chrome or Edge browser for best compatibility',
          'Make sure you\'re using HTTPS (localhost:8080 should work)',
          'Grant USB permission when prompted by the browser'
        ];
      
      case 'bluetooth':
        return [
          'Put your EC58 printer in pairing mode (hold feed button for 3-5 seconds)',
          'First pair the printer in Windows Bluetooth settings',
          'Go to Windows Settings → Bluetooth & devices → Add device',
          'Select your printer and complete pairing',
          'Then try connecting in this app',
          'Use Chrome or Edge browser (Firefox doesn\'t support Web Bluetooth)',
          'Make sure you\'re using HTTPS'
        ];
      
      case 'network':
        return [
          'Connect your printer to the same WiFi network',
          'Print a test page to get the printer\'s IP address',
          'Enter the IP address in the network settings',
          'Use port 9100 (default for most thermal printers)',
          'Make sure your firewall allows the connection'
        ];
    }
  };

  const getCommonIssues = () => {
    const common = [
      {
        issue: 'Browser Compatibility',
        solution: 'Use Chrome or Edge for best support. Firefox has limited support, Safari is not recommended.',
        icon: <Info className="h-4 w-4 text-blue-500" />
      },
      {
        issue: 'HTTPS Required',
        solution: 'Web USB and Bluetooth APIs require HTTPS. localhost should work fine.',
        icon: <AlertTriangle className="h-4 w-4 text-yellow-500" />
      },
      {
        issue: 'Printer Not in Pairing Mode',
        solution: 'For Bluetooth: Hold the feed button for 3-5 seconds until you hear a beep.',
        icon: <Bluetooth className="h-4 w-4 text-blue-500" />
      },
      {
        issue: 'USB Permission Denied',
        solution: 'Make sure to grant USB access when the browser prompts you.',
        icon: <Usb className="h-4 w-4 text-green-500" />
      }
    ];

    return common;
  };

  const getBrowserSupport = () => {
    return [
      { browser: 'Chrome', usb: true, bluetooth: true, network: true },
      { browser: 'Edge', usb: true, bluetooth: true, network: true },
      { browser: 'Firefox', usb: true, bluetooth: false, network: true },
      { browser: 'Safari', usb: false, bluetooth: false, network: true }
    ];
  };

  return (
    <div className="space-y-4">
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <XCircle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-700">
            <strong>Connection Failed:</strong> {error}
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getConnectionIcon()}
            {connectionType.toUpperCase()} Connection Steps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="space-y-2">
            {getConnectionSteps().map((step, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </span>
                <span className="text-sm">{step}</span>
              </li>
            ))}
          </ol>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Common Issues & Solutions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {getCommonIssues().map((item, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                {item.icon}
                <div>
                  <h4 className="font-medium text-sm">{item.issue}</h4>
                  <p className="text-sm text-gray-600 mt-1">{item.solution}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            Browser Compatibility
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {getBrowserSupport().map((browser, index) => (
              <div key={index} className="flex items-center justify-between p-2 border rounded">
                <span className="font-medium">{browser.browser}</span>
                <div className="flex gap-2">
                  <Badge variant={browser.usb ? "default" : "secondary"}>
                    USB {browser.usb ? '✓' : '✗'}
                  </Badge>
                  <Badge variant={browser.bluetooth ? "default" : "secondary"}>
                    Bluetooth {browser.bluetooth ? '✓' : '✗'}
                  </Badge>
                  <Badge variant={browser.network ? "default" : "secondary"}>
                    Network {browser.network ? '✓' : '✗'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-500" />
        <AlertDescription className="text-green-700">
          <strong>Tip:</strong> If you can't get the thermal printer working, don't worry! 
          The system will automatically fall back to browser printing, so your operations won't be interrupted.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default PrinterTroubleshooting;
