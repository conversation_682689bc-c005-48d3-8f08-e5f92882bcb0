import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowLeft,
  Search,
  AlertTriangle,
  Package,
  Plus,
  Minus,
  Edit,
  RefreshCw,
  Download,
  Upload,
  TrendingDown,
  TrendingUp,
  Eye,
  ChevronDown,
  Brain,
  Lightbulb
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { completeMenuItems } from "@/data/completeMenuData";
import { useToast } from "@/hooks/use-toast";
import {
  getAllInventoryItems,
  addInventoryItem,
  updateInventoryItem,
  recordTransaction,
  recordWaste,
  getInventoryStats,
  getLowStockItems,
  getExpiringItems,
  searchItems,
  formatCurrency,
  formatDate,
  type InventoryItem
} from "@/utils/inventoryStorage";
import { AIInsights } from "@/components/inventory/AIInsights";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { populateSampleData } from "@/utils/sampleInventoryData";

const InventoryManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [activeTab, setActiveTab] = useState<'inventory' | 'insights'>('inventory');
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [updateQuantity, setUpdateQuantity] = useState("");
  const [isAddStockDialogOpen, setIsAddStockDialogOpen] = useState(false);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [inventoryStats, setInventoryStats] = useState({
    totalItems: 0,
    lowStockItems: 0,
    expiringItems: 0,
    totalValue: 0,
    monthlyWaste: 0,
    wastePercentage: 0
  });
  const [newStockForm, setNewStockForm] = useState({
    name: "",
    category: "vegetables" as InventoryItem['category'],
    currentStock: "",
    minStockLevel: "",
    maxStockLevel: "",
    unit: "kg" as InventoryItem['unit'],
    costPerUnit: "",
    supplier: "",
    expiryDate: ""
  });

  // Load inventory data
  const loadInventoryData = () => {
    const items = getAllInventoryItems();
    const stats = getInventoryStats();
    setInventoryItems(items);
    setInventoryStats(stats);
  };

  useEffect(() => {
    loadInventoryData();
  }, []);

  // Categories for inventory items
  const categories = [
    "all",
    "vegetables",
    "meat",
    "dairy",
    "grains",
    "spices",
    "beverages",
    "other"
  ];
  const statuses = ["all", "critical", "low", "good", "overstocked"];

  const formatCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "critical": return "bg-red-100 text-red-800 border-red-200";
      case "low": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "good": return "bg-green-100 text-green-800 border-green-200";
      case "overstocked": return "bg-blue-100 text-blue-800 border-blue-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStockStatus = (item: InventoryItem) => {
    if (item.currentStock <= item.minStockLevel * 0.5) return "critical";
    if (item.currentStock <= item.minStockLevel) return "low";
    if (item.currentStock >= item.maxStockLevel * 0.9) return "overstocked";
    return "good";
  };

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.supplier && item.supplier.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === "all" || item.category === selectedCategory;
    const matchesStatus = selectedStatus === "all" || getStockStatus(item) === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const handleUpdateStock = (item: InventoryItem) => {
    setSelectedItem(item);
    setUpdateQuantity(item.currentStock.toString());
    setIsUpdateDialogOpen(true);
  };

  const handleStockUpdate = () => {
    if (!selectedItem) return;

    try {
      const newStock = parseInt(updateQuantity);
      if (isNaN(newStock) || newStock < 0) {
        toast({
          title: "Invalid Input",
          description: "Please enter a valid positive number",
          variant: "destructive",
        });
        return;
      }

      // Record transaction for the stock change
      const difference = newStock - selectedItem.currentStock;
      if (difference !== 0) {
        recordTransaction({
          itemId: selectedItem.id,
          itemName: selectedItem.name,
          type: difference > 0 ? 'purchase' : 'usage',
          quantity: Math.abs(difference),
          reason: difference > 0 ? 'Stock adjustment - increase' : 'Stock adjustment - decrease',
          performedBy: 'admin', // TODO: Get from auth
          cost: difference > 0 ? Math.abs(difference) * selectedItem.costPerUnit : undefined
        });
      }

      // Update the item
      updateInventoryItem(selectedItem.id, { currentStock: newStock });

      toast({
        title: "Stock Updated",
        description: `${selectedItem.name} stock updated to ${newStock} ${selectedItem.unit}`,
      });

      loadInventoryData(); // Refresh data
      setIsUpdateDialogOpen(false);
      setSelectedItem(null);
      setUpdateQuantity("");
    } catch (error) {
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    }
  };

  const handleQuickAdjust = (item: InventoryItem, adjustment: number) => {
    try {
      const newStock = Math.max(0, item.currentStock + adjustment);

      // Record transaction
      recordTransaction({
        itemId: item.id,
        itemName: item.name,
        type: adjustment > 0 ? 'purchase' : 'usage',
        quantity: Math.abs(adjustment),
        reason: adjustment > 0 ? 'Quick adjustment - increase' : 'Quick adjustment - decrease',
        performedBy: 'admin', // TODO: Get from auth
        cost: adjustment > 0 ? Math.abs(adjustment) * item.costPerUnit : undefined
      });

      toast({
        title: "Stock Adjusted",
        description: `${item.name} stock adjusted to ${newStock} ${item.unit}`,
      });

      loadInventoryData(); // Refresh data
    } catch (error) {
      toast({
        title: "Adjustment Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    }
  };

  const handleExportInventory = () => {
    try {
      const data = {
        exportDate: new Date().toISOString(),
        inventoryItems: inventoryItems,
        stats: inventoryStats
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `inventory-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: "Inventory data exported successfully!",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export inventory data",
        variant: "destructive",
      });
    }
  };

  const handleAddNewStock = () => {
    setNewStockForm({
      name: "",
      category: "vegetables",
      currentStock: "",
      minStockLevel: "",
      maxStockLevel: "",
      unit: "kg",
      costPerUnit: "",
      supplier: "",
      expiryDate: ""
    });
    setIsAddStockDialogOpen(true);
  };

  const handleSaveNewStock = () => {
    // Validate form
    if (!newStockForm.name || !newStockForm.currentStock ||
        !newStockForm.minStockLevel || !newStockForm.maxStockLevel ||
        !newStockForm.costPerUnit || !newStockForm.supplier) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields!",
        variant: "destructive",
      });
      return;
    }

    // Validate stock numbers
    const currentStock = parseInt(newStockForm.currentStock);
    const minStock = parseInt(newStockForm.minStockLevel);
    const maxStock = parseInt(newStockForm.maxStockLevel);
    const price = parseFloat(newStockForm.costPerUnit);

    if (currentStock < 0 || minStock < 0 || maxStock < 0 || price < 0) {
      toast({
        title: "Validation Error",
        description: "Stock quantities and price must be positive numbers!",
        variant: "destructive",
      });
      return;
    }

    if (minStock >= maxStock) {
      toast({
        title: "Validation Error",
        description: "Maximum stock must be greater than minimum stock!",
        variant: "destructive",
      });
      return;
    }

    try {
      // Add new inventory item
      const newItem = addInventoryItem({
        name: newStockForm.name,
        category: newStockForm.category,
        currentStock: currentStock,
        unit: newStockForm.unit,
        minStockLevel: minStock,
        maxStockLevel: maxStock,
        costPerUnit: price,
        supplier: newStockForm.supplier,
        expiryDate: newStockForm.expiryDate || undefined,
        purchaseDate: new Date().toISOString(),
        notes: `Initial stock entry - ${new Date().toLocaleDateString()}`
      });

      toast({
        title: "Stock Item Added",
        description: `${newItem.name} has been added to inventory with ${newItem.currentStock} ${newItem.unit}`,
      });

      loadInventoryData(); // Refresh data
      setIsAddStockDialogOpen(false);
      setNewStockForm({
        name: "",
        category: "vegetables",
        currentStock: "",
        minStockLevel: "",
        maxStockLevel: "",
        unit: "kg",
        costPerUnit: "",
        supplier: "",
        expiryDate: ""
      });
    } catch (error) {
      toast({
        title: "Failed to Add Item",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    }
  };

  const handleNewStockFormChange = (field: string, value: string) => {
    setNewStockForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between px-4 py-3 gap-4">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600 shrink-0 h-10 w-10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-xl font-bold text-gray-900 leading-tight truncate">Inventory Management</h1>
              <p className="text-sm text-gray-500 leading-tight truncate">Track and manage stock levels</p>
            </div>
          </div>
          <div className="flex items-center gap-2 shrink-0 ml-4">
            <Button
              variant="default"
              size="sm"
              onClick={handleAddNewStock}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 h-10 px-3 whitespace-nowrap"
            >
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add New Stock</span>
              <span className="sm:hidden">Add</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportInventory}
              className="flex items-center gap-2 h-10 px-3 whitespace-nowrap"
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Export</span>
            </Button>
            {inventoryItems.length === 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  populateSampleData();
                  loadInventoryData();
                  toast({
                    title: "Sample Data Added",
                    description: "Sample inventory items have been added for testing",
                  });
                }}
                className="flex items-center gap-2 h-10 px-3 whitespace-nowrap"
              >
                <Upload className="h-4 w-4" />
                <span className="hidden sm:inline">Add Sample Data</span>
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                loadInventoryData();
                toast({
                  title: "Data Refreshed",
                  description: "Inventory data has been refreshed",
                });
              }}
              className="text-gray-600 h-10 w-10"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b bg-white">
        <div className="px-6">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('inventory')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'inventory'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Inventory Management
              </div>
            </button>
            <button
              onClick={() => setActiveTab('insights')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'insights'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <Brain className="h-4 w-4" />
                AI Insights
              </div>
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {activeTab === 'inventory' && (
          <>
            {/* Inventory Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Total Items</p>
                  <p className="text-2xl font-bold">{inventoryStats.totalItems}</p>
                </div>
                <Package className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm">Critical Stock</p>
                  <p className="text-2xl font-bold">
                    {inventoryItems.filter(item => getStockStatus(item) === "critical").length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100 text-sm">Low Stock</p>
                  <p className="text-2xl font-bold">{inventoryStats.lowStockItems}</p>
                </div>
                <TrendingDown className="h-8 w-8 text-yellow-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Total Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(inventoryStats.totalValue)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[180px] text-sm">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category === "all" ? "All Categories" : category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[140px] text-sm">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {statuses.map(status => (
                      <SelectItem key={status} value={status}>
                        {status === "all" ? "All Status" : status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Inventory Items */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredItems.map((item) => {
            const status = getStockStatus(item);
            const stockPercentage = (item.currentStock / item.maxStockLevel) * 100;

            return (
              <Card key={item.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-bold text-lg">{item.name}</h3>
                        <Badge className={`${getStatusColor(status)} text-xs`}>
                          {status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">{formatCategoryName(item.category)}</p>
                      <p className="text-xs text-gray-500">Supplier: {item.supplier || 'Not specified'}</p>
                      {item.expiryDate && (
                        <p className="text-xs text-orange-600">
                          Expires: {formatDate(item.expiryDate)}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateStock(item)}
                        className="ml-2"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Current Stock</span>
                        <span className="font-medium">
                          {item.currentStock} / {item.maxStockLevel} {item.unit}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            status === "critical" ? "bg-red-500" :
                            status === "low" ? "bg-yellow-500" :
                            status === "overstocked" ? "bg-blue-500" : "bg-green-500"
                          }`}
                          style={{ width: `${Math.min(stockPercentage, 100)}%` }}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Min Stock</p>
                        <p className="font-medium">{item.minStockLevel} {item.unit}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Max Stock</p>
                        <p className="font-medium">{item.maxStockLevel} {item.unit}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Unit Price</p>
                        <p className="font-medium">{formatCurrency(item.costPerUnit)}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Total Value</p>
                        <p className="font-medium">{formatCurrency(item.currentStock * item.costPerUnit)}</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-3 border-t">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAdjust(item, -1)}
                          className="h-8 w-8 p-0"
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="text-sm font-medium px-2">{item.currentStock}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickAdjust(item, 1)}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500">Updated: {formatDate(item.lastUpdated)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredItems.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No items found matching your criteria</p>
            </CardContent>
          </Card>
        )}
          </>
        )}

        {activeTab === 'insights' && (
          <ErrorBoundary>
            <AIInsights
              inventoryItems={inventoryItems}
              inventoryStats={inventoryStats}
              onRefresh={loadInventoryData}
            />
          </ErrorBoundary>
        )}
      </div>

      {/* Update Stock Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Stock - {selectedItem?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="quantity">Current Stock</Label>
              <Input
                id="quantity"
                type="number"
                value={updateQuantity}
                onChange={(e) => setUpdateQuantity(e.target.value)}
                placeholder="Enter quantity"
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Unit: {selectedItem?.unit} | Min: {selectedItem?.minStockLevel} | Max: {selectedItem?.maxStockLevel}
              </p>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleStockUpdate}>
                Update Stock
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add New Stock Dialog */}
      <Dialog open={isAddStockDialogOpen} onOpenChange={setIsAddStockDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Stock Item</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="itemName">Item Name *</Label>
                <Input
                  id="itemName"
                  value={newStockForm.name}
                  onChange={(e) => handleNewStockFormChange('name', e.target.value)}
                  placeholder="Enter item name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={newStockForm.category} onValueChange={(value) => handleNewStockFormChange('category', value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vegetables">Vegetables</SelectItem>
                    <SelectItem value="meat">Meat</SelectItem>
                    <SelectItem value="dairy">Dairy</SelectItem>
                    <SelectItem value="grains">Grains</SelectItem>
                    <SelectItem value="spices">Spices</SelectItem>
                    <SelectItem value="beverages">Beverages</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="currentStock">Current Stock *</Label>
                <Input
                  id="currentStock"
                  type="number"
                  value={newStockForm.currentStock}
                  onChange={(e) => handleNewStockFormChange('currentStock', e.target.value)}
                  placeholder="0"
                  className="mt-1"
                  min="0"
                />
              </div>
              <div>
                <Label htmlFor="minStockLevel">Minimum Stock *</Label>
                <Input
                  id="minStockLevel"
                  type="number"
                  value={newStockForm.minStockLevel}
                  onChange={(e) => handleNewStockFormChange('minStockLevel', e.target.value)}
                  placeholder="0"
                  className="mt-1"
                  min="0"
                />
              </div>
              <div>
                <Label htmlFor="maxStockLevel">Maximum Stock *</Label>
                <Input
                  id="maxStockLevel"
                  type="number"
                  value={newStockForm.maxStockLevel}
                  onChange={(e) => handleNewStockFormChange('maxStockLevel', e.target.value)}
                  placeholder="0"
                  className="mt-1"
                  min="0"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="unit">Unit *</Label>
                <Select value={newStockForm.unit} onValueChange={(value) => handleNewStockFormChange('unit', value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kg">Kilograms (kg)</SelectItem>
                    <SelectItem value="grams">Grams (g)</SelectItem>
                    <SelectItem value="liters">Liters (L)</SelectItem>
                    <SelectItem value="ml">Milliliters (ml)</SelectItem>
                    <SelectItem value="pieces">Pieces</SelectItem>
                    <SelectItem value="packets">Packets</SelectItem>
                    <SelectItem value="bottles">Bottles</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="costPerUnit">Unit Price (₹) *</Label>
                <Input
                  id="costPerUnit"
                  type="number"
                  value={newStockForm.costPerUnit}
                  onChange={(e) => handleNewStockFormChange('costPerUnit', e.target.value)}
                  placeholder="0.00"
                  className="mt-1"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="supplier">Supplier *</Label>
                <Input
                  id="supplier"
                  value={newStockForm.supplier}
                  onChange={(e) => handleNewStockFormChange('supplier', e.target.value)}
                  placeholder="Enter supplier name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
                <Input
                  id="expiryDate"
                  type="date"
                  value={newStockForm.expiryDate}
                  onChange={(e) => handleNewStockFormChange('expiryDate', e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Total Value Preview */}
            {newStockForm.currentStock && newStockForm.price && (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-blue-900">Total Stock Value:</span>
                  <span className="text-lg font-bold text-blue-900">
                    ₹{(parseInt(newStockForm.currentStock || '0') * parseFloat(newStockForm.price || '0')).toLocaleString()}
                  </span>
                </div>
                <p className="text-xs text-blue-700 mt-1">
                  {newStockForm.currentStock} {newStockForm.unit} × ₹{newStockForm.price} per {newStockForm.unit}
                </p>
              </div>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsAddStockDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveNewStock} className="bg-blue-600 hover:bg-blue-700">
                Add Stock Item
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InventoryManagement;
