// Test utility for notification system
import {
  createEarlyCheckoutNotification,
  updateNotificationStatus,
  getAllNotifications,
  getNotificationStats,
  getPendingNotificationsCount,
  getStaffNotifications,
  clearAllNotifications,
  type EarlyCheckoutNotification
} from './notificationStorage';

export const testNotificationSystem = () => {
  console.log('🧪 Testing Notification System...');
  
  try {
    // Clear existing notifications for clean test
    clearAllNotifications();
    console.log('✅ Cleared existing notifications');
    
    // Test 1: Create early checkout notifications
    console.log('\n📝 Test 1: Creating Early Checkout Notifications');
    
    const testStaff = [
      { id: 'staff-001', name: '<PERSON>', phone: '9876543210' },
      { id: 'staff-002', name: '<PERSON><PERSON>', phone: '**********' },
      { id: 'staff-003', name: '<PERSON><PERSON> <PERSON>', phone: '7654321098' }
    ];
    
    const notifications: EarlyCheckoutNotification[] = [];
    
    testStaff.forEach((staff, index) => {
      const scheduledEndTime = new Date();
      scheduledEndTime.setHours(18, 0, 0, 0); // 6:00 PM
      
      const actualCheckoutTime = new Date();
      actualCheckoutTime.setHours(17, 30 - (index * 10), 0, 0); // 5:30, 5:20, 5:10 PM
      
      const notification = createEarlyCheckoutNotification(
        staff.id,
        staff.name,
        staff.phone,
        scheduledEndTime,
        actualCheckoutTime,
        `Test reason for ${staff.name} - ${index === 0 ? 'Family emergency' : index === 1 ? 'Medical appointment' : 'Personal work'}`
      );
      
      notifications.push(notification);
      console.log(`✅ Created notification for ${staff.name} (${notification.minutesEarly} min early)`);
    });
    
    // Test 2: Check notification stats
    console.log('\n📊 Test 2: Checking Notification Stats');
    const stats = getNotificationStats();
    console.log(`✅ Total notifications: ${stats.total}`);
    console.log(`✅ Pending notifications: ${stats.pending}`);
    console.log(`✅ Today's requests: ${stats.todayRequests}`);
    
    // Test 3: Get pending notifications count
    console.log('\n🔔 Test 3: Pending Notifications Count');
    const pendingCount = getPendingNotificationsCount();
    console.log(`✅ Pending count: ${pendingCount}`);
    
    // Test 4: Get staff notifications
    console.log('\n👤 Test 4: Staff Notifications');
    testStaff.forEach(staff => {
      const staffNotifs = getStaffNotifications(staff.id);
      console.log(`✅ ${staff.name} has ${staffNotifs.length} notifications`);
    });
    
    // Test 5: Admin approval workflow
    console.log('\n✅ Test 5: Admin Approval Workflow');
    
    // Approve first notification
    const firstNotification = notifications[0];
    const approvedNotification = updateNotificationStatus(
      firstNotification.id,
      'approved',
      'admin-001',
      'Admin User',
      'Approved due to family emergency'
    );
    
    if (approvedNotification) {
      console.log(`✅ Approved notification for ${approvedNotification.staffName}`);
      console.log(`   Admin response: ${approvedNotification.adminResponse?.comments}`);
    }
    
    // Reject second notification
    const secondNotification = notifications[1];
    const rejectedNotification = updateNotificationStatus(
      secondNotification.id,
      'rejected',
      'admin-001',
      'Admin User',
      'Please complete your shift as scheduled'
    );
    
    if (rejectedNotification) {
      console.log(`✅ Rejected notification for ${rejectedNotification.staffName}`);
      console.log(`   Admin response: ${rejectedNotification.adminResponse?.comments}`);
    }
    
    // Test 6: Final stats after admin actions
    console.log('\n📈 Test 6: Final Statistics');
    const finalStats = getNotificationStats();
    console.log(`✅ Total: ${finalStats.total}`);
    console.log(`✅ Pending: ${finalStats.pending}`);
    console.log(`✅ Approved: ${finalStats.approved}`);
    console.log(`✅ Rejected: ${finalStats.rejected}`);
    
    // Test 7: Real-time event system
    console.log('\n🔄 Test 7: Real-time Event System');
    
    let eventReceived = false;
    const eventHandler = (event: CustomEvent) => {
      eventReceived = true;
      console.log('✅ Received notificationsChanged event');
      console.log(`   Event detail contains ${event.detail.notifications.length} notifications`);
    };
    
    window.addEventListener('notificationsChanged', eventHandler as EventListener);
    
    // Create another notification to trigger event
    const eventTestNotification = createEarlyCheckoutNotification(
      'staff-004',
      'Test Staff',
      '9999999999',
      new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
      new Date(), // now
      'Testing event system'
    );
    
    // Give a moment for event to fire
    setTimeout(() => {
      if (eventReceived) {
        console.log('✅ Real-time event system working correctly');
      } else {
        console.log('❌ Real-time event system not working');
      }
      
      window.removeEventListener('notificationsChanged', eventHandler as EventListener);
    }, 100);
    
    console.log('\n🎉 Notification System Test Complete!');
    console.log('📋 Summary:');
    console.log(`   - Created ${notifications.length + 1} test notifications`);
    console.log(`   - Tested admin approval/rejection workflow`);
    console.log(`   - Verified real-time event system`);
    console.log(`   - All core functionality working correctly`);
    
    return {
      success: true,
      notifications: getAllNotifications(),
      stats: getNotificationStats()
    };
    
  } catch (error) {
    console.error('❌ Notification system test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Helper function to create sample notifications for demo
export const createSampleNotifications = () => {
  console.log('📝 Creating sample notifications for demo...');
  
  const sampleStaff = [
    { id: 'staff-001', name: 'Raj Kumar', phone: '9876543210' },
    { id: 'staff-002', name: 'Sunita Devi', phone: '**********' }
  ];
  
  sampleStaff.forEach((staff, index) => {
    const scheduledEndTime = new Date();
    scheduledEndTime.setHours(18, 0, 0, 0);
    
    const actualCheckoutTime = new Date();
    actualCheckoutTime.setHours(17, 25 + (index * 5), 0, 0);
    
    createEarlyCheckoutNotification(
      staff.id,
      staff.name,
      staff.phone,
      scheduledEndTime,
      actualCheckoutTime,
      index === 0 ? 'Family emergency - need to pick up child from school' : 'Medical appointment that couldn\'t be rescheduled'
    );
  });
  
  console.log('✅ Sample notifications created');
};

// Helper to clear all notifications
export const clearTestNotifications = () => {
  clearAllNotifications();
  console.log('✅ All test notifications cleared');
};
