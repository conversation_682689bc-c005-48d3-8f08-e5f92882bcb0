import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Printer,
  Clock,
  Users,
  Utensils,
  FileText,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle
} from "lucide-react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { kotStorage, KOT, KOTItem } from "@/utils/kotStorage";
import { format } from "date-fns";
import PrintService from "@/services/printService";

const KOTDetails = () => {
  const navigate = useNavigate();
  const { kotId } = useParams();
  const location = useLocation();
  const { toast } = useToast();
  const [kot, setKot] = useState<KOT | null>(null);
  const [loading, setLoading] = useState(true);

  // Get KOT from location state or fetch by ID
  useEffect(() => {
    const existingKOT = location.state?.existingKOT;
    
    if (existingKOT) {
      setKot(existingKOT);
      setLoading(false);
    } else if (kotId) {
      // Try to find KOT by kotNumber
      const allKOTs = kotStorage.getAllKOTs();
      const foundKOT = allKOTs.find(k => k.kotNumber === kotId);
      if (foundKOT) {
        setKot(foundKOT);
      }
      setLoading(false);
    } else {
      setLoading(false);
    }
  }, [kotId, location.state]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today, ${format(date, "h:mm a")}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${format(date, "h:mm a")}`;
    } else {
      return format(date, "dd MMM yyyy, h:mm a");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "completed":
        return "Completed";
      case "cancelled":
        return "Cancelled";
      default:
        return status;
    }
  };

  const handlePrintKOT = async () => {
    if (!kot) return;

    try {
      // Print the KOT using unified print service
      const success = await PrintService.printKOT(kot);

      if (success) {
        toast({
          title: "KOT Printed Successfully!",
          description: `KOT #${kot.kotNumber} printed successfully`,
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      console.error('Failed to print KOT:', error);
      toast({
        title: "Print Error",
        description: "Failed to print KOT. Please try again.",
        variant: "destructive"
      });

      // Fallback to browser printing
      const printContent = `
        <div style="font-family: Arial, sans-serif; max-width: 300px; margin: 0 auto;">
          <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 15px;">
            <h2 style="margin: 0;">Wok Ka Tadka</h2>
            <p style="margin: 5px 0;">Kitchen Order Ticket</p>
          </div>

          <div style="margin-bottom: 15px;">
            <p><strong>KOT #:</strong> ${kot.kotNumber}</p>
            <p><strong>Table:</strong> ${kot.tableId}</p>
            <p><strong>Date:</strong> ${format(new Date(kot.createdAt), "dd/MM/yyyy")}</p>
            <p><strong>Time:</strong> ${format(new Date(kot.createdAt), "h:mm a")}</p>
            <p><strong>Status:</strong> ${getStatusText(kot.status)}</p>
          </div>

          <div style="border-top: 1px solid #000; padding-top: 10px; margin-bottom: 15px;">
            <h3 style="margin: 0 0 10px 0;">Items:</h3>
            ${kot.items.map(item => `
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>${item.name} x${item.quantity}</span>
                <span>₹${item.price * item.quantity}</span>
              </div>
            `).join('')}
          </div>

          ${kot.specialInstructions ? `
            <div style="border-top: 1px solid #000; padding-top: 10px; margin-bottom: 15px;">
              <p><strong>Special Instructions:</strong></p>
              <p>${kot.specialInstructions}</p>
            </div>
          ` : ''}

          <div style="border-top: 2px solid #000; padding-top: 10px; text-align: center;">
            <p style="font-size: 18px; font-weight: bold;">Total: ₹${kot.totalAmount}</p>
          </div>
        </div>
      `;

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head><title>KOT ${kot.kotNumber}</title></head>
            <body>${printContent}</body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const completeKOT = () => {
    if (!kot) return;
    
    const success = kotStorage.completeKOT(kot.kotNumber);
    if (success) {
      setKot({ ...kot, status: 'completed' });
      toast({
        title: "KOT Completed",
        description: `KOT #${kot.kotNumber} has been marked as completed.`,
      });
    }
  };

  const cancelKOT = () => {
    if (!kot) return;
    
    const success = kotStorage.cancelKOT(kot.kotNumber);
    if (success) {
      setKot({ ...kot, status: 'cancelled' });
      toast({
        title: "KOT Cancelled",
        description: `KOT #${kot.kotNumber} has been cancelled.`,
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading KOT details...</p>
        </div>
      </div>
    );
  }

  if (!kot) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">KOT Not Found</h2>
            <p className="text-gray-600 mb-4">The requested KOT could not be found.</p>
            <Button onClick={() => navigate("/delivery/kot-history", { replace: true })}>
              Back to KOT History
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4 apk-header-fixed">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/kot-history")}
              className="text-white hover:bg-white/20"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">KOT Details</h1>
              <p className="text-white/80 text-sm">Table {kot.tableId}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePrintKOT}
            className="text-white hover:bg-white/20"
          >
            <Printer className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-4 apk-content-with-header">
        {/* KOT Summary Card */}
        <Card className="shadow-sm border-0 bg-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <span className="font-bold text-xl text-gray-900">KOT #{kot.kotNumber}</span>
                <Badge className={`${getStatusColor(kot.status)} text-sm px-3 py-1`}>
                  {getStatusText(kot.status)}
                </Badge>
              </div>
              <p className="font-bold text-2xl text-primary">₹{kot.totalAmount}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2 text-gray-600">
                <Users className="h-4 w-4" />
                <span>Table {kot.tableId}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Utensils className="h-4 w-4" />
                <span>{kot.items.length} items</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>{format(new Date(kot.createdAt), "dd MMM yyyy")}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{format(new Date(kot.createdAt), "h:mm a")}</span>
              </div>
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <span>Created {formatDate(kot.createdAt)}</span>
              {kot.versions && kot.versions.length > 1 && (
                <span className="ml-2 text-blue-600 font-medium">• {kot.versions.length} versions</span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Items List */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Utensils className="h-5 w-5" />
              Order Items ({kot.items.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {kot.items.map((item: KOTItem, index: number) => (
                <div key={index} className="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">{item.name}</span>
                      {item.veg ? (
                        <div className="w-4 h-4 border border-green-500 flex items-center justify-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                      ) : (
                        <div className="w-4 h-4 border border-red-500 flex items-center justify-center">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        </div>
                      )}
                      {item.spicy > 0 && (
                        <span className="text-red-500 text-xs">
                          {'🌶️'.repeat(item.spicy)}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      ₹{item.price} × {item.quantity}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">₹{item.price * item.quantity}</div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total Amount:</span>
                <span className="text-primary">₹{kot.totalAmount}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Special Instructions */}
        {kot.specialInstructions && (
          <Card className="shadow-sm border-0 bg-yellow-50 border-yellow-200">
            <CardContent className="p-4">
              <h4 className="font-semibold text-yellow-800 mb-2 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Special Instructions
              </h4>
              <p className="text-yellow-700">{kot.specialInstructions}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Action Buttons */}
      {kot.status === 'active' && (
        <div className="apk-footer-fixed bg-white border-t shadow-lg p-4">
          <div className="flex gap-3">
            <Button
              variant="outline"
              size="lg"
              className="flex-1 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"
              onClick={completeKOT}
            >
              <CheckCircle className="h-5 w-5 mr-2" />
              Complete KOT
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="flex-1 border-red-500 text-red-600 hover:bg-red-500 hover:text-white"
              onClick={cancelKOT}
            >
              <XCircle className="h-5 w-5 mr-2" />
              Cancel KOT
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default KOTDetails;
