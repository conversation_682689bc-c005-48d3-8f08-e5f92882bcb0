// Working Hours Management Utility for staff attendance system

import { getStaffById } from './staffPinStorage';

export interface WorkingHours {
  startTime: string; // HH:MM format (24-hour)
  endTime: string; // HH:MM format (24-hour)
  totalHours: number; // Total working hours per day
  workingDays: string[]; // Array of working days ['monday', 'tuesday', etc.]
  breakDuration: number; // Break duration in minutes
  overtimeThreshold: number; // Minutes after which overtime starts
  earlyCheckoutThreshold: number; // Minutes before end time that requires approval
}

export interface WorkingHoursSettings {
  workingHours: WorkingHours;
  lastUpdated: string;
  updatedBy: string;
}

const STORAGE_KEY = 'wok_ka_tadka_working_hours';
const BACKUP_STORAGE_KEY = 'wok_ka_tadka_working_hours_backup';

// Error handling for working hours
export class WorkingHoursError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'WorkingHoursError';
  }
}

// Create backup of working hours settings
const createWorkingHoursBackup = (settings: WorkingHoursSettings): void => {
  try {
    const backup = {
      timestamp: new Date().toISOString(),
      data: settings,
      version: '1.0'
    };
    localStorage.setItem(BACKUP_STORAGE_KEY, JSON.stringify(backup));
  } catch (error) {
    console.warn('Failed to create working hours backup:', error);
  }
};

// Restore working hours from backup
const restoreWorkingHoursFromBackup = (): WorkingHoursSettings | null => {
  try {
    const backupData = localStorage.getItem(BACKUP_STORAGE_KEY);
    if (backupData) {
      const backup = JSON.parse(backupData);
      if (backup.data && backup.data.workingHours) {
        console.log('Restored working hours from backup');
        return backup.data;
      }
    }
  } catch (error) {
    console.error('Failed to restore working hours from backup:', error);
  }
  return null;
};

// Default working hours configuration
const DEFAULT_WORKING_HOURS: WorkingHours = {
  startTime: '09:00',
  endTime: '18:00',
  totalHours: 8,
  workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
  breakDuration: 60, // 1 hour break
  overtimeThreshold: 30, // 30 minutes after end time
  earlyCheckoutThreshold: 30 // 30 minutes before end time requires approval
};

// Get working hours settings from localStorage with error handling
export const getWorkingHoursSettings = (): WorkingHoursSettings => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);

      // Validate the structure
      if (parsed.workingHours && typeof parsed.workingHours === 'object') {
        return parsed;
      } else {
        console.warn('Invalid working hours structure, attempting backup restore');
        throw new WorkingHoursError('Invalid working hours structure', 'INVALID_STRUCTURE');
      }
    }
  } catch (error) {
    console.error('Error loading working hours settings:', error);

    // Try to restore from backup
    const backupSettings = restoreWorkingHoursFromBackup();
    if (backupSettings) {
      // Save the restored settings
      saveWorkingHoursSettings(backupSettings);
      return backupSettings;
    }
  }

  // Return default settings as last resort
  const defaultSettings = {
    workingHours: DEFAULT_WORKING_HOURS,
    lastUpdated: new Date().toISOString(),
    updatedBy: 'system'
  };

  // Save default settings
  saveWorkingHoursSettings(defaultSettings);
  return defaultSettings;
};

// Save working hours settings to localStorage
export const saveWorkingHoursSettings = (settings: WorkingHoursSettings): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    
    // Dispatch custom event to notify other components
    window.dispatchEvent(new CustomEvent('workingHoursChanged', {
      detail: settings
    }));
  } catch (error) {
    console.error('Error saving working hours settings:', error);
    throw new Error('Failed to save working hours settings');
  }
};

// Update working hours (convenience function)
export const updateWorkingHours = (workingHours: WorkingHours, updatedBy: string = 'admin'): void => {
  const settings: WorkingHoursSettings = {
    workingHours,
    lastUpdated: new Date().toISOString(),
    updatedBy
  };
  
  saveWorkingHoursSettings(settings);
};

// Get current working hours
export const getCurrentWorkingHours = (): WorkingHours => {
  const settings = getWorkingHoursSettings();
  return settings.workingHours;
};

// Check if current time is within working hours
export const isWithinWorkingHours = (currentTime?: Date): boolean => {
  const now = currentTime || new Date();
  const workingHours = getCurrentWorkingHours();
  
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;
  
  const [startHour, startMinute] = workingHours.startTime.split(':').map(Number);
  const [endHour, endMinute] = workingHours.endTime.split(':').map(Number);
  
  const startTimeInMinutes = startHour * 60 + startMinute;
  const endTimeInMinutes = endHour * 60 + endMinute;
  
  return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes <= endTimeInMinutes;
};

// Update isEarlyCheckout to accept staffId and use per-staff working hours if set
export const isEarlyCheckout = (checkoutTime?: Date, staffId?: string): boolean => {
  const now = checkoutTime || new Date();
  let workingHours;
  if (staffId) {
    const staff = getStaffById(staffId);
    workingHours = staff?.workingHours || getCurrentWorkingHours();
  } else {
    workingHours = getCurrentWorkingHours();
  }
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;
  const [endHour, endMinute] = workingHours.endTime.split(':').map(Number);
  const endTimeInMinutes = endHour * 60 + endMinute;
  const earlyThresholdInMinutes = endTimeInMinutes - workingHours.earlyCheckoutThreshold;
  return currentTimeInMinutes < earlyThresholdInMinutes;
};

// Get working hours display string
export const getWorkingHoursDisplay = (): string => {
  const workingHours = getCurrentWorkingHours();
  
  // Convert 24-hour to 12-hour format
  const formatTime = (time: string): string => {
    const [hour, minute] = time.split(':').map(Number);
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minute.toString().padStart(2, '0')} ${period}`;
  };
  
  return `${formatTime(workingHours.startTime)} - ${formatTime(workingHours.endTime)}`;
};

// Calculate total hours from start and end time
export const calculateTotalHours = (startTime: string, endTime: string): number => {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  const startTimeInMinutes = startHour * 60 + startMinute;
  const endTimeInMinutes = endHour * 60 + endMinute;
  
  const totalMinutes = endTimeInMinutes - startTimeInMinutes;
  return Math.round((totalMinutes / 60) * 100) / 100; // Round to 2 decimal places
};

// Get working days display
export const getWorkingDaysDisplay = (): string => {
  const workingHours = getCurrentWorkingHours();
  const dayNames = {
    monday: 'Mon',
    tuesday: 'Tue',
    wednesday: 'Wed',
    thursday: 'Thu',
    friday: 'Fri',
    saturday: 'Sat',
    sunday: 'Sun'
  };
  
  return workingHours.workingDays.map(day => dayNames[day as keyof typeof dayNames]).join(', ');
};

// Reset to default settings
export const resetWorkingHoursToDefault = (): void => {
  const settings: WorkingHoursSettings = {
    workingHours: DEFAULT_WORKING_HOURS,
    lastUpdated: new Date().toISOString(),
    updatedBy: 'admin'
  };

  saveWorkingHoursSettings(settings);
};

// Validation functions for working hours
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Validate working hours configuration
export const validateWorkingHours = (workingHours: WorkingHours): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate time format
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(workingHours.startTime)) {
    errors.push('Start time must be in HH:MM format (24-hour)');
  }
  if (!timeRegex.test(workingHours.endTime)) {
    errors.push('End time must be in HH:MM format (24-hour)');
  }

  // Validate time logic
  if (timeRegex.test(workingHours.startTime) && timeRegex.test(workingHours.endTime)) {
    const [startHour, startMinute] = workingHours.startTime.split(':').map(Number);
    const [endHour, endMinute] = workingHours.endTime.split(':').map(Number);

    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;

    if (startTimeInMinutes >= endTimeInMinutes) {
      errors.push('End time must be after start time');
    }

    const actualHours = (endTimeInMinutes - startTimeInMinutes) / 60;
    if (Math.abs(actualHours - workingHours.totalHours) > 0.1) {
      warnings.push(`Total hours (${workingHours.totalHours}) doesn't match calculated hours (${actualHours.toFixed(1)})`);
    }

    // Check for reasonable working hours
    if (actualHours > 12) {
      warnings.push('Working hours exceed 12 hours per day - consider staff wellbeing');
    }
    if (actualHours < 4) {
      warnings.push('Working hours are less than 4 hours per day');
    }
  }

  // Validate total hours
  if (workingHours.totalHours <= 0 || workingHours.totalHours > 24) {
    errors.push('Total hours must be between 0 and 24');
  }

  // Validate working days
  const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  if (!Array.isArray(workingHours.workingDays) || workingHours.workingDays.length === 0) {
    errors.push('At least one working day must be selected');
  } else {
    const invalidDays = workingHours.workingDays.filter(day => !validDays.includes(day));
    if (invalidDays.length > 0) {
      errors.push(`Invalid working days: ${invalidDays.join(', ')}`);
    }

    if (workingHours.workingDays.length === 7) {
      warnings.push('Staff are scheduled to work all 7 days - consider providing rest days');
    }
  }

  // Validate break duration
  if (workingHours.breakDuration < 0 || workingHours.breakDuration > 480) { // Max 8 hours break
    errors.push('Break duration must be between 0 and 480 minutes');
  }

  if (workingHours.breakDuration > workingHours.totalHours * 60 * 0.5) {
    warnings.push('Break duration is more than 50% of total working hours');
  }

  // Validate overtime threshold
  if (workingHours.overtimeThreshold < 0 || workingHours.overtimeThreshold > 120) {
    errors.push('Overtime threshold must be between 0 and 120 minutes');
  }

  // Validate early checkout threshold
  if (workingHours.earlyCheckoutThreshold < 0 || workingHours.earlyCheckoutThreshold > 240) {
    errors.push('Early checkout threshold must be between 0 and 240 minutes');
  }

  if (workingHours.earlyCheckoutThreshold > workingHours.totalHours * 60 * 0.25) {
    warnings.push('Early checkout threshold is more than 25% of total working hours');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Validate and update working hours with comprehensive checks
export const validateAndUpdateWorkingHours = (
  workingHours: WorkingHours,
  updatedBy: string = 'admin'
): { success: boolean; validation: ValidationResult } => {
  const validation = validateWorkingHours(workingHours);

  if (validation.isValid) {
    // Auto-calculate total hours if there's a discrepancy
    const [startHour, startMinute] = workingHours.startTime.split(':').map(Number);
    const [endHour, endMinute] = workingHours.endTime.split(':').map(Number);
    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;
    const calculatedHours = (endTimeInMinutes - startTimeInMinutes) / 60;

    const correctedWorkingHours = {
      ...workingHours,
      totalHours: Math.round(calculatedHours * 100) / 100 // Round to 2 decimal places
    };

    updateWorkingHours(correctedWorkingHours, updatedBy);
    return { success: true, validation };
  }

  return { success: false, validation };
};

// Check if current day is a working day
export const isWorkingDay = (date?: Date): boolean => {
  const checkDate = date || new Date();
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const dayName = dayNames[checkDate.getDay()];

  const workingHours = getCurrentWorkingHours();
  return workingHours.workingDays.includes(dayName);
};

// Get next working day
export const getNextWorkingDay = (fromDate?: Date): Date => {
  const startDate = fromDate || new Date();
  let nextDay = new Date(startDate);
  nextDay.setDate(nextDay.getDate() + 1);

  // Look for next working day (max 7 days ahead)
  for (let i = 0; i < 7; i++) {
    if (isWorkingDay(nextDay)) {
      return nextDay;
    }
    nextDay.setDate(nextDay.getDate() + 1);
  }

  // If no working day found in next 7 days, return tomorrow
  const tomorrow = new Date(startDate);
  tomorrow.setDate(tomorrow.getDate() + 1);
  return tomorrow;
};

// Check if time is within break hours (if break schedule is implemented)
export const isBreakTime = (currentTime?: Date): boolean => {
  // This is a placeholder for break time logic
  // In a full implementation, you might have specific break times
  // For now, we'll assume no specific break times are configured
  return false;
};

// Get working hours summary for display
export const getWorkingHoursSummary = (): string => {
  const workingHours = getCurrentWorkingHours();
  const validation = validateWorkingHours(workingHours);

  let summary = `${getWorkingHoursDisplay()} • ${workingHours.totalHours}h/day • ${getWorkingDaysDisplay()}`;

  if (workingHours.breakDuration > 0) {
    summary += ` • ${workingHours.breakDuration}min break`;
  }

  if (!validation.isValid) {
    summary += ' ⚠️';
  }

  return summary;
};
