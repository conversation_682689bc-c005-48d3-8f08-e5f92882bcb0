import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Download,
  Upload,
  Database,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Trash2,
  HardDrive
} from "lucide-react";
import { Logo } from "@/components/ui/logo";

const Backup = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  // Mock backup history
  const [backupHistory] = useState([
    {
      id: 1,
      date: "2025-01-15",
      time: "14:30:00",
      size: "2.4 MB",
      type: "Auto",
      status: "Success"
    },
    {
      id: 2,
      date: "2025-01-14",
      time: "14:30:00",
      size: "2.3 MB",
      type: "Auto",
      status: "Success"
    },
    {
      id: 3,
      date: "2025-01-13",
      time: "16:45:00",
      size: "2.2 MB",
      type: "Manual",
      status: "Success"
    },
    {
      id: 4,
      date: "2025-01-12",
      time: "14:30:00",
      size: "2.1 MB",
      type: "Auto",
      status: "Failed"
    }
  ]);

  const handleCreateBackup = async () => {
    setIsBackingUp(true);
    
    try {
      // Simulate backup process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Get all data from localStorage
      const backupData = {
        timestamp: new Date().toISOString(),
        kots: JSON.parse(localStorage.getItem('kots') || '[]'),
        orders: JSON.parse(localStorage.getItem('orders') || '[]'),
        inventory: JSON.parse(localStorage.getItem('inventory') || '[]'),
        staff: JSON.parse(localStorage.getItem('staff') || '[]'),
        settings: {
          restaurant: JSON.parse(localStorage.getItem('restaurantSettings') || '{}'),
          notifications: JSON.parse(localStorage.getItem('notificationSettings') || '{}'),
          system: JSON.parse(localStorage.getItem('systemSettings') || '{}')
        }
      };

      // Create and download backup file
      const dataStr = JSON.stringify(backupData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `wok-ka-tadka-backup-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Backup Created Successfully!",
        description: "Your data has been backed up and downloaded.",
      });
    } catch (error) {
      toast({
        title: "Backup Failed",
        description: "There was an error creating the backup.",
        variant: "destructive"
      });
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleRestoreBackup = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      setIsRestoring(true);
      
      try {
        const text = await file.text();
        const backupData = JSON.parse(text);
        
        // Restore data to localStorage
        if (backupData.kots) localStorage.setItem('kots', JSON.stringify(backupData.kots));
        if (backupData.orders) localStorage.setItem('orders', JSON.stringify(backupData.orders));
        if (backupData.inventory) localStorage.setItem('inventory', JSON.stringify(backupData.inventory));
        if (backupData.staff) localStorage.setItem('staff', JSON.stringify(backupData.staff));
        if (backupData.settings?.restaurant) localStorage.setItem('restaurantSettings', JSON.stringify(backupData.settings.restaurant));
        if (backupData.settings?.notifications) localStorage.setItem('notificationSettings', JSON.stringify(backupData.settings.notifications));
        if (backupData.settings?.system) localStorage.setItem('systemSettings', JSON.stringify(backupData.settings.system));

        toast({
          title: "Backup Restored Successfully!",
          description: "Your data has been restored from the backup file.",
        });
      } catch (error) {
        toast({
          title: "Restore Failed",
          description: "Invalid backup file or corrupted data.",
          variant: "destructive"
        });
      } finally {
        setIsRestoring(false);
      }
    };
    input.click();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Success":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Failed":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Success":
        return "bg-green-100 text-green-700";
      case "Failed":
        return "bg-red-100 text-red-700";
      default:
        return "bg-yellow-100 text-yellow-700";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/admin/dashboard")}
                className="flex items-center gap-1 sm:gap-2 shrink-0"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <Logo className="h-6 sm:h-8 shrink-0" />
              <div className="min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Backup & Restore</h1>
                <p className="text-xs sm:text-sm text-gray-600 truncate">Manage your data backups</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 space-y-4 sm:space-y-6">
        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Download className="h-4 w-4 sm:h-5 sm:w-5" />
                Create Backup
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4 text-sm sm:text-base">
                Create a complete backup of all your restaurant data including orders, KOTs, inventory, and settings.
              </p>
              <Button
                onClick={handleCreateBackup}
                disabled={isBackingUp}
                className="w-full h-12 sm:h-14 text-sm sm:text-base"
              >
                {isBackingUp ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Creating Backup...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Create Backup Now
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Upload className="h-4 w-4 sm:h-5 sm:w-5" />
                Restore Backup
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4 text-sm sm:text-base">
                Restore your restaurant data from a previously created backup file. This will overwrite current data.
              </p>
              <Button
                onClick={handleRestoreBackup}
                disabled={isRestoring}
                variant="outline"
                className="w-full h-12 sm:h-14 text-sm sm:text-base"
              >
                {isRestoring ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Restoring...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Restore from File
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Backup History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Database className="h-4 w-4 sm:h-5 sm:w-5" />
              Backup History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {backupHistory.map((backup) => (
                <div key={backup.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-3 sm:p-4 bg-gray-50 rounded-lg gap-3 sm:gap-4">
                  <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
                    <div className="shrink-0">
                      {getStatusIcon(backup.status)}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex flex-wrap items-center gap-2 mb-1">
                        <span className="font-medium text-sm sm:text-base">{backup.date}</span>
                        <Badge variant="outline" className="text-xs">
                          {backup.type}
                        </Badge>
                        <Badge className={`text-xs ${getStatusColor(backup.status)}`}>
                          {backup.status}
                        </Badge>
                      </div>
                      <div className="text-xs sm:text-sm text-gray-600 flex flex-wrap items-center gap-2 sm:gap-4">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {backup.time}
                        </span>
                        <span className="flex items-center gap-1">
                          <HardDrive className="h-3 w-3" />
                          {backup.size}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2 shrink-0 justify-end sm:justify-start">
                    {backup.status === "Success" && (
                      <Button variant="outline" size="sm" className="text-xs sm:text-sm h-8 sm:h-9">
                        <Download className="h-3 w-3 mr-1" />
                        <span className="hidden sm:inline">Download</span>
                      </Button>
                    )}
                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 h-8 sm:h-9 px-2 sm:px-3">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Storage Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <HardDrive className="h-4 w-4 sm:h-5 sm:w-5" />
              Storage Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Database className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-xl sm:text-2xl font-bold text-blue-600">2.4 MB</div>
                <div className="text-xs sm:text-sm text-gray-600">Current Data Size</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <CheckCircle className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2" />
                <div className="text-xl sm:text-2xl font-bold text-green-600">15</div>
                <div className="text-xs sm:text-sm text-gray-600">Successful Backups</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <Calendar className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-xl sm:text-2xl font-bold text-orange-600">Today</div>
                <div className="text-xs sm:text-sm text-gray-600">Last Backup</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Backup;
