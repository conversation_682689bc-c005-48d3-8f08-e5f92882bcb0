
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, MapPin, Clock, Phone, Navigation } from "lucide-react";

const CurrentOrders = () => {
  const navigate = useNavigate();
  
  const currentOrders = [
    {
      id: "#56970",
      customerName: "<PERSON><PERSON>",
      address: "B-204, Sector 18, Noida",
      amount: 345,
      status: "picked",
      time: "2 mins ago",
      distance: "2.3 km",
      duration: "8 mins"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ready": return "bg-warning text-warning-foreground";
      case "picked": return "bg-primary text-primary-foreground";
      case "delivered": return "bg-success text-success-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ready": return "Ready for Pickup";
      case "picked": return "Out for Delivery";
      case "delivered": return "Delivered";
      default: return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">Current Orders</h1>
              <p className="text-white/80 text-sm">Orders in progress</p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {currentOrders.length > 0 ? (
          currentOrders.map((order) => (
            <Card key={order.id} className="shadow-sm border-0 bg-white hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-gray-900">{order.id}</span>
                    <Badge className={`${getStatusColor(order.status)} text-xs px-2 py-1`}>
                      {getStatusText(order.status)}
                    </Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`tel:${order.customerName}`)}
                    className="border-primary text-primary hover:bg-primary hover:text-white"
                  >
                    <Phone className="h-4 w-4 mr-1" />
                    Call
                  </Button>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="font-semibold text-gray-900">{order.customerName}</p>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>{order.address}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-primary" />
                        <span>{order.distance}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-primary" />
                        <span>{order.duration}</span>
                      </div>
                    </div>
                    <p className="font-bold text-lg text-primary">₹{order.amount}</p>
                  </div>

                  <div className="flex gap-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-primary text-primary hover:bg-primary hover:text-white"
                      onClick={() => window.open(`https://maps.google.com/?q=${encodeURIComponent(order.address)}`)}
                    >
                      <Navigation className="h-4 w-4 mr-1" />
                      Navigate
                    </Button>
                    <Button
                      variant="delivery"
                      size="sm"
                      className="flex-1"
                      onClick={() => navigate(`/delivery/details/${encodeURIComponent(order.id)}`)}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <div className="bg-gray-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Clock className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Current Orders</h3>
            <p className="text-gray-500">You don't have any orders in progress right now</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CurrentOrders;
