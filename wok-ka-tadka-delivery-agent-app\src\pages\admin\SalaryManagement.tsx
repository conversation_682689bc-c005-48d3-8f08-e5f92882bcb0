import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  ArrowLeft, 
  DollarSign,
  Users,
  Calendar,
  CreditCard,
  Plus,
  Eye,
  Search,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import {
  getAllSalaryRecords,
  getSalarySummary,
  makeSalaryPayment,
  formatMonth,
  getActiveAdvancesForStaff,
  type SalaryRecord,
  type SalaryPayment
} from "@/utils/salaryStorage";
import { getAllStaffPins } from "@/utils/staffPinStorage";
import { useToast } from "@/hooks/use-toast";
import { getStaffAttendanceRecords, formatDate } from "@/utils/attendanceStorage";
import { useMemo } from "react";

const SalaryManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [salaryRecords, setSalaryRecords] = useState<SalaryRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<SalaryRecord[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [staffList, setStaffList] = useState<any[]>([]);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedSalaryRecord, setSelectedSalaryRecord] = useState<SalaryRecord | null>(null);
  const [paymentData, setPaymentData] = useState({
    amount: "",
    paymentMethod: "cash" as SalaryPayment['paymentMethod'],
    transactionId: "",
    notes: ""
  });
  const [showAttendance, setShowAttendance] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterRecords();
  }, [salaryRecords, searchTerm, selectedMonth, selectedStatus]);

  const loadData = () => {
    // Load salary records - filter out admin and manager roles
    const allRecords = getAllSalaryRecords();
    const records = allRecords.filter(record =>
      record.staffRole !== 'admin' &&
      record.staffRole !== 'manager'
    );
    setSalaryRecords(records);

    // Load staff list
    const staff = getAllStaffPins();
    setStaffList(staff);
  };

  const filterRecords = () => {
    let filtered = salaryRecords;

    // Filter by month
    if (selectedMonth) {
      filtered = filtered.filter(record => record.month === selectedMonth);
    }

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(record => record.status === selectedStatus);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(record => 
        record.staffName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.staffPhone.includes(searchTerm)
      );
    }

    // Sort by staff name
    filtered.sort((a, b) => a.staffName.localeCompare(b.staffName));

    setFilteredRecords(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'partial':
        return <Clock className="h-4 w-4" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const handlePaySalary = (record: SalaryRecord) => {
    setSelectedSalaryRecord(record);
    setPaymentData({
      amount: (record.finalSalary - record.paidAmount).toString(),
      paymentMethod: "cash",
      transactionId: "",
      notes: ""
    });
    setIsPaymentDialogOpen(true);
  };

  const handleMakePayment = async () => {
    if (!selectedSalaryRecord) return;

    const amount = parseFloat(paymentData.amount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid payment amount",
        variant: "destructive",
      });
      return;
    }

    const remainingAmount = selectedSalaryRecord.finalSalary - selectedSalaryRecord.paidAmount;
    if (amount > remainingAmount) {
      toast({
        title: "Amount Exceeds Balance",
        description: `Maximum payable amount is ₹${remainingAmount}`,
        variant: "destructive",
      });
      return;
    }

    try {
      makeSalaryPayment(
        selectedSalaryRecord.id,
        amount,
        paymentData.paymentMethod,
        "admin", // In real app, this would be the logged-in admin's ID
        paymentData.transactionId || undefined,
        paymentData.notes || undefined
      );

      toast({
        title: "Payment Successful",
        description: `₹${amount} paid to ${selectedSalaryRecord.staffName}`,
      });

      // Reload data
      loadData();
      setIsPaymentDialogOpen(false);
      setSelectedSalaryRecord(null);
    } catch (error) {
      toast({
        title: "Payment Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const summary = getSalarySummary();

  const getMonthAttendance = (staffId: string, month: string) => {
    // month: YYYY-MM
    const records = getStaffAttendanceRecords(staffId);
    return records.filter(r => r.date.startsWith(month));
  };

  const getAttendanceColor = (record: any) => {
    const dateObj = new Date(record.date);
    const day = dateObj.getDay();
    if (record.status === 'absent') return 'bg-red-100';
    if ((day === 0 || day === 6) && record.status !== 'absent') return 'bg-yellow-100';
    if (record.status === 'checked-in' || record.status === 'checked-out') return 'bg-white';
    return 'bg-gray-100';
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-white truncate">Salary Management</h1>
              <p className="text-white/80 text-xs sm:text-sm truncate">Manage staff salaries & payments</p>
            </div>
          </div>
          <div className="flex gap-1 sm:gap-2 shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/admin/advance-management")}
              className="text-white hover:bg-white/20 px-2 sm:px-3"
            >
              <CreditCard className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Advances</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/admin/generate-salary")}
              className="text-white hover:bg-white/20 px-2 sm:px-3"
            >
              <Plus className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Generate</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-blue-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{summary.totalStaff}</p>
                <p className="text-sm text-gray-500">Total Staff</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">₹{summary.totalPaid.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Total Paid</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-red-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">₹{summary.totalPending.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Total Pending</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-amber-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-amber-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{summary.pendingCount}</p>
                <p className="text-sm text-gray-500">Pending Payments</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-purple-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-purple-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">₹{summary.totalActiveAdvances?.toLocaleString() || '0'}</p>
                <p className="text-sm text-gray-500">Active Advances</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="shadow-sm border-0 bg-white">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search staff..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Input
                type="month"
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value)}
                className="w-full"
              />
              
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="partial">Partial</option>
                <option value="paid">Paid</option>
              </select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedMonth(new Date().toISOString().slice(0, 7));
                  setSelectedStatus("all");
                }}
                className="w-full"
              >
                <Filter className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Salary Records */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Salary Records ({filteredRecords.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {filteredRecords.length === 0 ? (
              <div className="text-center py-8">
                <DollarSign className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No salary records found</p>
                <p className="text-sm text-gray-400">Try adjusting your filters or generate salary records</p>
              </div>
            ) : (
              filteredRecords.map((record) => (
                <div
                  key={record.id}
                  className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  {/* Header Section */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-3">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(record.status)}
                        <span className="font-semibold text-gray-900">
                          {record.staffName}
                        </span>
                      </div>
                      <Badge className={`${getStatusColor(record.status)} text-xs px-2 py-1`}>
                        {record.status.toUpperCase()}
                      </Badge>
                      <span className="text-sm text-gray-500 capitalize">
                        {record.staffRole}
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/salary-details/${record.id}`)}
                        className="text-xs"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      {record.status !== 'paid' && (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handlePaySalary(record)}
                          className="text-xs"
                        >
                          <CreditCard className="h-3 w-3 mr-1" />
                          Pay
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Salary Information Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 text-sm mb-3">
                    <div>
                      <span className="text-gray-500">Month:</span>
                      <span className="font-medium ml-2">
                        {formatMonth(record.month)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Base Salary:</span>
                      <span className="font-medium ml-2">₹{record.baseSalary.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Final Salary:</span>
                      <span className="font-medium ml-2">₹{record.finalSalary.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Paid:</span>
                      <span className="font-medium ml-2">₹{record.paidAmount.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Balance:</span>
                      <span className="font-bold ml-2 text-red-600">
                        ₹{(record.finalSalary - record.paidAmount).toLocaleString()}
                      </span>
                    </div>
                  </div>

                  {/* Additional Details Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                    <div>
                      <span className="text-gray-500">Present Days:</span>
                      <span className="font-medium ml-2">{record.presentDays}/{record.workingDays}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Total Hours:</span>
                      <span className="font-medium ml-2">{record.totalHours}h</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Overtime:</span>
                      <span className="font-medium ml-2">{record.overtimeHours}h</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Deductions:</span>
                      <span className="font-medium ml-2">₹{record.deductions.toLocaleString()}</span>
                      {getActiveAdvancesForStaff(record.staffId).length > 0 && (
                        <span className="text-xs text-blue-600 ml-1">(incl. advances)</span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* Payment Dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Make Salary Payment</DialogTitle>
          </DialogHeader>
          {selectedSalaryRecord && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold">{selectedSalaryRecord.staffName}</h3>
                <p className="text-sm text-gray-600">
                  {formatMonth(selectedSalaryRecord.month)} • {selectedSalaryRecord.staffRole}
                </p>
                <div className="mt-2 text-sm">
                  <div className="flex justify-between">
                    <span>Final Salary:</span>
                    <span>₹{selectedSalaryRecord.finalSalary.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Already Paid:</span>
                    <span>₹{selectedSalaryRecord.paidAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t pt-1 mt-1">
                    <span>Balance:</span>
                    <span>₹{(selectedSalaryRecord.finalSalary - selectedSalaryRecord.paidAmount).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div className="mb-2">
                <button
                  className="text-blue-600 underline text-sm mb-2"
                  onClick={() => setShowAttendance((v) => !v)}
                  type="button"
                >
                  {showAttendance ? "Hide" : "Show"} Monthly Attendance
                </button>
                {showAttendance && (
                  <div className="border rounded p-2 bg-gray-50 max-h-64 overflow-y-auto">
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                      {getMonthAttendance(selectedSalaryRecord.staffId, selectedSalaryRecord.month).map((rec) => {
                        const dateObj = new Date(rec.date);
                        const day = dateObj.getDay();
                        let label = "Full Day";
                        let color = getAttendanceColor(rec);
                        if (rec.status === 'absent') label = "Absent";
                        else if (day === 0 || day === 6) label = "Half Day (Weekend)";
                        return (
                          <div key={rec.id} className={`rounded p-2 text-xs ${color} border flex flex-col items-start`}>
                            <span className="font-semibold">{formatDate(rec.date)}</span>
                            <span>{label}</span>
                            <span className="text-gray-500">{rec.status.replace('-', ' ')}</span>
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-2 text-xs">
                      <span className="inline-block w-3 h-3 bg-white border mr-1"></span> Full Day
                      <span className="inline-block w-3 h-3 bg-yellow-100 border mx-2"></span> Half Day (Sat/Sun)
                      <span className="inline-block w-3 h-3 bg-red-100 border mx-2"></span> Absent
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">Payment Amount</label>
                  <Input
                    type="number"
                    placeholder="Enter amount"
                    value={paymentData.amount}
                    onChange={(e) => setPaymentData({...paymentData, amount: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Payment Method</label>
                  <select
                    value={paymentData.paymentMethod}
                    onChange={(e) => setPaymentData({...paymentData, paymentMethod: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm"
                  >
                    <option value="cash">Cash</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="upi">UPI</option>
                    <option value="cheque">Cheque</option>
                  </select>
                </div>

                {paymentData.paymentMethod !== 'cash' && (
                  <div>
                    <label className="text-sm font-medium">Transaction ID</label>
                    <Input
                      placeholder="Enter transaction ID"
                      value={paymentData.transactionId}
                      onChange={(e) => setPaymentData({...paymentData, transactionId: e.target.value})}
                    />
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium">Notes (Optional)</label>
                  <Input
                    placeholder="Payment notes"
                    value={paymentData.notes}
                    onChange={(e) => setPaymentData({...paymentData, notes: e.target.value})}
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsPaymentDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleMakePayment}
                  className="flex-1"
                >
                  Make Payment
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalaryManagement;
