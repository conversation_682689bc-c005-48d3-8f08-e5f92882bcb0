import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  ArrowLeft, 
  Calculator,
  Users,
  Calendar,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Clock
} from "lucide-react";
import {
  createSalaryRecord,
  getCurrentMonth,
  formatMonth,
  type SalaryRecord
} from "@/utils/salaryStorage";
import { getAllStaffPins } from "@/utils/staffPinStorage";
import { getStaffAttendanceRecords } from "@/utils/attendanceStorage";
import { useToast } from "@/hooks/use-toast";

const SalaryGeneration = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [staffList, setStaffList] = useState<any[]>([]);
  const [selectedMonth, setSelectedMonth] = useState(getCurrentMonth());
  const [workingDays, setWorkingDays] = useState(26);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedRecords, setGeneratedRecords] = useState<SalaryRecord[]>([]);
  const [staffSalaryData, setStaffSalaryData] = useState<{[key: string]: any}>({});

  useEffect(() => {
    loadStaffData();
  }, [selectedMonth]);

  const loadStaffData = async () => {
    // Only include staff roles that should receive salaries (exclude admin and manager)
    const staff = getAllStaffPins().filter(s =>
      s.isActive &&
      s.role !== 'manager'
    );
    setStaffList(staff);

    // Load attendance data for each staff member
    const salaryData: {[key: string]: any} = {};

    for (const staffMember of staff) {
      const records = getStaffAttendanceRecords(staffMember.id).filter(r => r.date.startsWith(selectedMonth));
      let fullDays = 0;
      let halfDays = 0;
      let absentDays = 0;
      for (const rec of records) {
        const dateObj = new Date(rec.date);
        const day = dateObj.getDay();
        if (rec.status === 'absent') {
          absentDays++;
        } else if (day === 0 || day === 6) {
          halfDays++;
        } else {
          fullDays++;
        }
      }
      const presentDays = fullDays + halfDays * 0.5;
      salaryData[staffMember.id] = {
        ...staffMember,
        baseSalary: getBaseSalaryByRole(staffMember.role),
        presentDays,
        fullDays,
        halfDays,
        absentDays,
        totalHours: records.reduce((sum, r) => sum + (r.totalHours || 0), 0),
        overtimeHours: 0, // update if needed
        bonuses: 0,
        deductions: 0
      };
    }

    setStaffSalaryData(salaryData);
  };

  const getBaseSalaryByRole = (role: string): number => {
    switch (role) {
      case 'waiter': return 18000;
      case 'delivery': return 20000;
      case 'kitchen': return 22000;
      default: return 15000;
    }
  };

  const updateStaffSalaryData = (staffId: string, field: string, value: number) => {
    setStaffSalaryData(prev => ({
      ...prev,
      [staffId]: {
        ...prev[staffId],
        [field]: value
      }
    }));
  };

  const calculateFinalSalary = (staffId: string) => {
    const data = staffSalaryData[staffId];
    if (!data) return 0;

    const dailySalary = data.baseSalary / workingDays;
    const basePay = dailySalary * data.presentDays;
    const overtimePay = (data.baseSalary / (workingDays * 8)) * data.overtimeHours * 1.5;
    const finalSalary = basePay + overtimePay + data.bonuses - data.deductions;
    
    return Math.round(finalSalary);
  };

  const handleGenerateSalaries = async () => {
    setIsGenerating(true);
    const records: SalaryRecord[] = [];

    try {
      for (const staffId of Object.keys(staffSalaryData)) {
        const data = staffSalaryData[staffId];
        
        const record = createSalaryRecord(
          staffId,
          data.name,
          data.phone,
          data.role,
          data.baseSalary,
          workingDays,
          data.presentDays,
          data.totalHours,
          data.overtimeHours,
          data.bonuses,
          data.deductions,
          selectedMonth
        );
        
        records.push(record);
      }

      setGeneratedRecords(records);
      
      toast({
        title: "Salaries Generated Successfully",
        description: `Generated salary records for ${records.length} staff members`,
      });

    } catch (error) {
      toast({
        title: "Error Generating Salaries",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGoToSalaryManagement = () => {
    navigate("/admin/salary-management");
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/salary-management")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">Generate Salaries</h1>
              <p className="text-white/80 text-sm">Create salary records based on attendance</p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Generation Settings */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Salary Generation Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Select Month</label>
                <Input
                  type="month"
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Working Days</label>
                <Input
                  type="number"
                  value={workingDays}
                  onChange={(e) => setWorkingDays(parseInt(e.target.value) || 26)}
                  min="20"
                  max="31"
                />
              </div>
              <div className="flex items-end">
                <Button
                  onClick={handleGenerateSalaries}
                  disabled={isGenerating || Object.keys(staffSalaryData).length === 0}
                  className="w-full"
                >
                  <Calculator className="h-4 w-4 mr-2" />
                  {isGenerating ? "Generating..." : "Generate Salaries"}
                </Button>
              </div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-blue-900">Generation Information</p>
                  <p className="text-blue-700 mt-1">
                    Salaries will be calculated based on attendance data for {formatMonth(selectedMonth)}. 
                    Base salaries are set according to staff roles. You can adjust individual values before generation.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Staff Salary Preview */}
        {Object.keys(staffSalaryData).length > 0 && (
          <Card className="shadow-sm border-0 bg-white">
            <CardHeader>
              <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Staff Salary Preview ({Object.keys(staffSalaryData).length} staff)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(staffSalaryData).map(([staffId, data]) => (
                <div key={staffId} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-gray-900">{data.name}</h3>
                      <p className="text-sm text-gray-600 capitalize">{data.role}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-green-600">
                        ₹{calculateFinalSalary(staffId).toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-500">Final Salary</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                    <div>
                      <label className="text-xs text-gray-500">Base Salary</label>
                      <Input
                        type="number"
                        value={data.baseSalary}
                        onChange={(e) => updateStaffSalaryData(staffId, 'baseSalary', parseInt(e.target.value) || 0)}
                        className="h-8 text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-500">Present Days</label>
                      <Input
                        type="number"
                        value={data.presentDays}
                        onChange={(e) => updateStaffSalaryData(staffId, 'presentDays', parseInt(e.target.value) || 0)}
                        className="h-8 text-sm"
                        max={workingDays}
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-500">Total Hours</label>
                      <Input
                        type="number"
                        value={data.totalHours}
                        onChange={(e) => updateStaffSalaryData(staffId, 'totalHours', parseFloat(e.target.value) || 0)}
                        className="h-8 text-sm"
                        step="0.5"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-500">Overtime Hours</label>
                      <Input
                        type="number"
                        value={data.overtimeHours}
                        onChange={(e) => updateStaffSalaryData(staffId, 'overtimeHours', parseFloat(e.target.value) || 0)}
                        className="h-8 text-sm"
                        step="0.5"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-500">Bonuses</label>
                      <Input
                        type="number"
                        value={data.bonuses}
                        onChange={(e) => updateStaffSalaryData(staffId, 'bonuses', parseInt(e.target.value) || 0)}
                        className="h-8 text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-500">Deductions</label>
                      <Input
                        type="number"
                        value={data.deductions}
                        onChange={(e) => updateStaffSalaryData(staffId, 'deductions', parseInt(e.target.value) || 0)}
                        className="h-8 text-sm"
                      />
                    </div>
                  </div>
                  {/* Show breakdown to admin */}
                  <div className="text-xs text-gray-600 mt-1">
                    <span>Full Days: {data.fullDays}</span> | <span>Half Days (Sat/Sun): {data.halfDays}</span> | <span>Absent: {data.absentDays}</span>
                    <br />
                    <span className="font-semibold">Counted Present Days: {data.presentDays}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Generation Results */}
        {generatedRecords.length > 0 && (
          <Card className="shadow-sm border-0 bg-white">
            <CardHeader>
              <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Generation Complete
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-green-50 p-4 rounded-lg mb-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  <div>
                    <p className="font-medium text-green-900">
                      Successfully generated {generatedRecords.length} salary records
                    </p>
                    <p className="text-green-700 text-sm mt-1">
                      Total salary amount: ₹{generatedRecords.reduce((sum, record) => sum + record.finalSalary, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleGoToSalaryManagement}
                  className="flex-1"
                >
                  <DollarSign className="h-4 w-4 mr-2" />
                  Go to Salary Management
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setGeneratedRecords([]);
                    loadStaffData();
                  }}
                  className="flex-1"
                >
                  Generate Again
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SalaryGeneration;
