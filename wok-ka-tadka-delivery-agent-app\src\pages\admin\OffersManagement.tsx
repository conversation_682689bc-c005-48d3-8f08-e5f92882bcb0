import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Plus,
  Percent,
  Calendar,
  Eye,
  EyeOff,
  RotateCcw,
  X,
  Gift,
  Tag,
  Clock,
  Users,
  IndianRupee
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";

interface Offer {
  id: string;
  title: string;
  description: string;
  discountType: "percentage" | "fixed";
  discountValue: number;
  minOrderAmount: number;
  maxDiscountAmount?: number;
  validFrom: string;
  validFromTime: string;
  validUntil: string;
  validUntilTime: string;
  isActive: boolean;
  usageLimit?: number;
  usedCount: number;
  targetCustomers: "all" | "new" | "existing";
  offerCode: string;
  backgroundColor: string;
  textColor: string;
  createdAt: string;
}

const OffersManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "inactive">("all");

  // Predefined color combinations for random selection
  const colorCombinations = [
    { backgroundColor: "#22C55E", textColor: "#FFFFFF" }, // Green
    { backgroundColor: "#EC4899", textColor: "#FFFFFF" }, // Dark Pink
    { backgroundColor: "#8B5CF6", textColor: "#FFFFFF" }, // Purple
    { backgroundColor: "#EAB308", textColor: "#FFFFFF" }, // Yellow
    { backgroundColor: "#F97316", textColor: "#FFFFFF" }, // Orange
  ];

  // Function to get random color combination
  const getRandomColorCombination = () => {
    const randomIndex = Math.floor(Math.random() * colorCombinations.length);
    return colorCombinations[randomIndex];
  };



  // Mock offers data
  const [offers, setOffers] = useState<Offer[]>([
    {
      id: "1",
      title: "8% off upto ₹100",
      description: "Get 8% discount on your first order above ₹499",
      discountType: "percentage",
      discountValue: 8,
      minOrderAmount: 499,
      maxDiscountAmount: 100,
      validFrom: "2024-01-01",
      validFromTime: "00:00",
      validUntil: "2024-12-31",
      validUntilTime: "23:59",
      isActive: true,
      usageLimit: 1000,
      usedCount: 245,
      targetCustomers: "new",
      offerCode: "FIRST8",
      backgroundColor: "#E91E63",
      textColor: "#FFFFFF",
      createdAt: "2024-01-01T10:00:00Z"
    },
    {
      id: "2",
      title: "10% off upto ₹150",
      description: "Save 10% on orders above ₹799",
      discountType: "percentage",
      discountValue: 10,
      minOrderAmount: 799,
      maxDiscountAmount: 150,
      validFrom: "2024-01-15",
      validFromTime: "09:00",
      validUntil: "2024-06-30",
      validUntilTime: "21:00",
      isActive: true,
      usageLimit: 500,
      usedCount: 89,
      targetCustomers: "all",
      offerCode: "SAVE10",
      backgroundColor: "#4CAF50",
      textColor: "#FFFFFF",
      createdAt: "2024-01-15T10:00:00Z"
    },
    {
      id: "3",
      title: "₹50 off on orders above ₹300",
      description: "Flat ₹50 discount on minimum order of ₹300",
      discountType: "fixed",
      discountValue: 50,
      minOrderAmount: 300,
      validFrom: "2023-12-01",
      validFromTime: "10:00",
      validUntil: "2024-01-31",
      validUntilTime: "22:00",
      isActive: false,
      usageLimit: 200,
      usedCount: 156,
      targetCustomers: "existing",
      offerCode: "FLAT50",
      backgroundColor: "#FF9800",
      textColor: "#FFFFFF",
      createdAt: "2023-12-01T10:00:00Z"
    }
  ]);

  const [newOffer, setNewOffer] = useState<Partial<Offer>>({
    title: "",
    description: "",
    discountType: "percentage",
    discountValue: 0,
    minOrderAmount: 0,
    maxDiscountAmount: 0,
    validFrom: "",
    validFromTime: "00:00",
    validUntil: "",
    validUntilTime: "23:59",
    usageLimit: 0,
    targetCustomers: "all",
    offerCode: ""
  });

  const filteredOffers = offers.filter(offer => {
    if (filterStatus === "active") return offer.isActive;
    if (filterStatus === "inactive") return !offer.isActive;
    return true;
  });

  const activeOffersCount = offers.filter(o => o.isActive).length;
  const inactiveOffersCount = offers.filter(o => !o.isActive).length;

  const handleCreateOffer = () => {
    if (!newOffer.title || !newOffer.description || !newOffer.offerCode) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    // Get random color combination
    const randomColors = getRandomColorCombination();

    const offer: Offer = {
      id: Date.now().toString(),
      title: newOffer.title!,
      description: newOffer.description!,
      discountType: newOffer.discountType!,
      discountValue: newOffer.discountValue!,
      minOrderAmount: newOffer.minOrderAmount!,
      maxDiscountAmount: newOffer.maxDiscountAmount,
      validFrom: newOffer.validFrom!,
      validFromTime: newOffer.validFromTime!,
      validUntil: newOffer.validUntil!,
      validUntilTime: newOffer.validUntilTime!,
      isActive: true,
      usageLimit: newOffer.usageLimit,
      usedCount: 0,
      targetCustomers: newOffer.targetCustomers!,
      offerCode: newOffer.offerCode!,
      backgroundColor: randomColors.backgroundColor,
      textColor: randomColors.textColor,
      createdAt: new Date().toISOString()
    };

    setOffers([offer, ...offers]);
    setIsCreateDialogOpen(false);
    setNewOffer({
      title: "",
      description: "",
      discountType: "percentage",
      discountValue: 0,
      minOrderAmount: 0,
      maxDiscountAmount: 0,
      validFrom: "",
      validFromTime: "00:00",
      validUntil: "",
      validUntilTime: "23:59",
      usageLimit: 0,
      targetCustomers: "all",
      offerCode: ""
    });

    toast({
      title: "Success!",
      description: "New offer has been created and activated",
    });
  };

  const handleDeactivateOffer = (offerId: string) => {
    setOffers(offers.map(offer => 
      offer.id === offerId ? { ...offer, isActive: false } : offer
    ));
    toast({
      title: "Offer Deactivated",
      description: "The offer has been deactivated and won't appear to customers",
    });
  };

  const handleReactivateOffer = (offerId: string) => {
    setOffers(offers.map(offer => 
      offer.id === offerId ? { ...offer, isActive: true } : offer
    ));
    toast({
      title: "Offer Reactivated",
      description: "The offer is now active and visible to customers",
    });
  };

  const handleDeleteOffer = (offerId: string) => {
    setOffers(offers.filter(offer => offer.id !== offerId));
    toast({
      title: "Offer Deleted",
      description: "The offer has been permanently deleted",
      variant: "destructive"
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const getDiscountText = (offer: Offer) => {
    if (offer.discountType === "percentage") {
      return `${offer.discountValue}% off${offer.maxDiscountAmount ? ` upto ₹${offer.maxDiscountAmount}` : ''}`;
    } else {
      return `₹${offer.discountValue} off`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-3 sm:p-4">
          <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <Logo className="h-6 sm:h-8 shrink-0" />
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Offers Management</h1>
              <p className="text-xs sm:text-sm text-gray-500 truncate">Create and manage customer offers</p>
            </div>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                <Plus className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">New Offer</span>
                <span className="sm:hidden">New</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Offer</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Offer Title *</Label>
                    <Input
                      id="title"
                      value={newOffer.title}
                      onChange={(e) => setNewOffer({...newOffer, title: e.target.value})}
                      placeholder="e.g., 10% off upto ₹150"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="offerCode">Offer Code *</Label>
                    <Input
                      id="offerCode"
                      value={newOffer.offerCode}
                      onChange={(e) => setNewOffer({...newOffer, offerCode: e.target.value.toUpperCase()})}
                      placeholder="e.g., SAVE10"
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={newOffer.description}
                    onChange={(e) => setNewOffer({...newOffer, description: e.target.value})}
                    placeholder="Describe your offer..."
                    className="mt-1"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="discountType">Discount Type</Label>
                    <Select
                      value={newOffer.discountType}
                      onValueChange={(value: "percentage" | "fixed") => setNewOffer({...newOffer, discountType: value})}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage (%)</SelectItem>
                        <SelectItem value="fixed">Fixed Amount (₹)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="discountValue">Discount Value</Label>
                    <Input
                      id="discountValue"
                      type="number"
                      value={newOffer.discountValue}
                      onChange={(e) => setNewOffer({...newOffer, discountValue: Number(e.target.value)})}
                      placeholder={newOffer.discountType === "percentage" ? "10" : "50"}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="minOrderAmount">Min Order Amount</Label>
                    <Input
                      id="minOrderAmount"
                      type="number"
                      value={newOffer.minOrderAmount}
                      onChange={(e) => setNewOffer({...newOffer, minOrderAmount: Number(e.target.value)})}
                      placeholder="299"
                      className="mt-1"
                    />
                  </div>
                </div>

                {newOffer.discountType === "percentage" && (
                  <div>
                    <Label htmlFor="maxDiscountAmount">Max Discount Amount (₹)</Label>
                    <Input
                      id="maxDiscountAmount"
                      type="number"
                      value={newOffer.maxDiscountAmount || ""}
                      onChange={(e) => setNewOffer({...newOffer, maxDiscountAmount: Number(e.target.value)})}
                      placeholder="100"
                      className="mt-1"
                    />
                  </div>
                )}

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="validFrom">Valid From</Label>
                    <div className="grid grid-cols-2 gap-2 mt-1">
                      <Input
                        id="validFrom"
                        type="date"
                        value={newOffer.validFrom}
                        onChange={(e) => setNewOffer({...newOffer, validFrom: e.target.value})}
                        placeholder="Date"
                      />
                      <Input
                        type="time"
                        value={newOffer.validFromTime}
                        onChange={(e) => setNewOffer({...newOffer, validFromTime: e.target.value})}
                        placeholder="Time"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="validUntil">Valid Until</Label>
                    <div className="grid grid-cols-2 gap-2 mt-1">
                      <Input
                        id="validUntil"
                        type="date"
                        value={newOffer.validUntil}
                        onChange={(e) => setNewOffer({...newOffer, validUntil: e.target.value})}
                        placeholder="Date"
                      />
                      <Input
                        type="time"
                        value={newOffer.validUntilTime}
                        onChange={(e) => setNewOffer({...newOffer, validUntilTime: e.target.value})}
                        placeholder="Time"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="usageLimit">Usage Limit (Optional)</Label>
                    <Input
                      id="usageLimit"
                      type="number"
                      value={newOffer.usageLimit || ""}
                      onChange={(e) => setNewOffer({...newOffer, usageLimit: Number(e.target.value)})}
                      placeholder="1000"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="targetCustomers">Target Customers</Label>
                    <Select
                      value={newOffer.targetCustomers}
                      onValueChange={(value: "all" | "new" | "existing") => setNewOffer({...newOffer, targetCustomers: value})}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Customers</SelectItem>
                        <SelectItem value="new">New Customers Only</SelectItem>
                        <SelectItem value="existing">Existing Customers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Color will be automatically selected from predefined combinations */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <Gift className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="text-sm">
                      <p className="font-medium text-blue-900">Automatic Color Selection</p>
                      <p className="text-blue-700 mt-1">
                        The offer card will automatically get a beautiful color combination from our predefined set of colors (Green, Dark Pink, Purple, Yellow, Orange).
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateOffer}>
                    Create Offer
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="p-3 sm:p-6 space-y-4 sm:space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-xs sm:text-sm">Total Offers</p>
                  <p className="text-2xl sm:text-3xl font-bold">{offers.length}</p>
                </div>
                <Gift className="h-8 w-8 sm:h-10 sm:w-10 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-xs sm:text-sm">Active Offers</p>
                  <p className="text-2xl sm:text-3xl font-bold">{activeOffersCount}</p>
                </div>
                <Eye className="h-8 w-8 sm:h-10 sm:w-10 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-xs sm:text-sm">Inactive Offers</p>
                  <p className="text-2xl sm:text-3xl font-bold">{inactiveOffersCount}</p>
                </div>
                <EyeOff className="h-8 w-8 sm:h-10 sm:w-10 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filter Tabs */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={filterStatus === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus("all")}
                className="text-xs sm:text-sm"
              >
                All Offers ({offers.length})
              </Button>
              <Button
                variant={filterStatus === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus("active")}
                className="text-xs sm:text-sm"
              >
                Active ({activeOffersCount})
              </Button>
              <Button
                variant={filterStatus === "inactive" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus("inactive")}
                className="text-xs sm:text-sm"
              >
                Inactive ({inactiveOffersCount})
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Offers List */}
        <div className="space-y-4">
          {filteredOffers.length > 0 ? (
            filteredOffers.map((offer) => (
              <Card key={offer.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex flex-col lg:flex-row gap-4">
                    {/* Offer Preview */}
                    <div
                      className="w-full lg:w-64 h-32 rounded-lg p-4 flex flex-col justify-between text-white shrink-0"
                      style={{ backgroundColor: offer.backgroundColor, color: offer.textColor }}
                    >
                      <div>
                        <h3 className="font-bold text-lg">{getDiscountText(offer)}</h3>
                        <p className="text-sm opacity-90 mt-1">USE {offer.offerCode}</p>
                        <p className="text-xs opacity-80 mt-1">ABOVE ₹{offer.minOrderAmount}</p>
                      </div>
                      <p className="text-xs opacity-75">{offer.description.substring(0, 50)}...</p>
                    </div>

                    {/* Offer Details */}
                    <div className="flex-1 space-y-3">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <div>
                          <h3 className="font-semibold text-lg">{offer.title}</h3>
                          <p className="text-sm text-gray-600">{offer.description}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={offer.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                            {offer.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500 flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            Code
                          </p>
                          <p className="font-medium">{offer.offerCode}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 flex items-center gap-1">
                            <IndianRupee className="h-3 w-3" />
                            Min Order
                          </p>
                          <p className="font-medium">₹{offer.minOrderAmount}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Valid Until
                          </p>
                          <p className="font-medium">{formatDate(offer.validUntil)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            Usage
                          </p>
                          <p className="font-medium">
                            {offer.usedCount}{offer.usageLimit ? `/${offer.usageLimit}` : ''}
                          </p>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-wrap gap-2 pt-2">
                        {offer.isActive ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeactivateOffer(offer.id)}
                            className="text-orange-600 border-orange-600 hover:bg-orange-50"
                          >
                            <EyeOff className="h-3 w-3 mr-1" />
                            Deactivate
                          </Button>
                        ) : (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleReactivateOffer(offer.id)}
                              className="text-green-600 border-green-600 hover:bg-green-50"
                            >
                              <RotateCcw className="h-3 w-3 mr-1" />
                              Reapply
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteOffer(offer.id)}
                              className="text-red-600 border-red-600 hover:bg-red-50"
                            >
                              <X className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No offers found matching your criteria</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default OffersManagement;
