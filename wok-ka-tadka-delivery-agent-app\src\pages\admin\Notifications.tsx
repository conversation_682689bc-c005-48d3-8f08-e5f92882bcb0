import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Bell,
  Clock,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  MessageSquare,
  AlertTriangle,
  RefreshCw,
  Filter,
  Search
} from "lucide-react";
import {
  getAllNotifications,
  getNotificationStats,
  updateNotificationStatus,
  getNotificationsByStatus,
  formatNotificationTime,
  getTimeAgo,
  type EarlyCheckoutNotification,
  type NotificationStats
} from "@/utils/notificationStorage";
import { markCheckOutWithTime } from "@/utils/attendanceStorage";

const Notifications = () => {
  const [notifications, setNotifications] = useState<EarlyCheckoutNotification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    todayRequests: 0
  });
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [selectedNotification, setSelectedNotification] = useState<EarlyCheckoutNotification | null>(null);
  const [adminComments, setAdminComments] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Load notifications and stats
  const loadData = () => {
    const allNotifications = getAllNotifications();
    const notificationStats = getNotificationStats();
    
    setNotifications(allNotifications);
    setStats(notificationStats);
  };

  useEffect(() => {
    loadData();
    
    // Listen for real-time updates
    const handleNotificationsChanged = () => {
      loadData();
    };
    
    window.addEventListener('notificationsChanged', handleNotificationsChanged);
    return () => window.removeEventListener('notificationsChanged', handleNotificationsChanged);
  }, []);

  // Filter notifications based on selected filter
  const filteredNotifications = selectedFilter === 'all' 
    ? notifications 
    : getNotificationsByStatus(selectedFilter);

  // Handle approve/reject
  const handleStatusUpdate = async (
    notificationId: string,
    status: 'approved' | 'rejected'
  ) => {
    if (!selectedNotification) return;

    setIsProcessing(true);
    try {
      const updatedNotification = updateNotificationStatus(
        notificationId,
        status,
        'admin-001', // TODO: Get actual admin ID from auth
        'Admin User', // TODO: Get actual admin name from auth
        adminComments.trim() || undefined
      );

      if (updatedNotification) {
        // If approved, mark the staff as checked out at the requested early time
        if (status === 'approved') {
          try {
            const checkoutRecord = markCheckOutWithTime(
              updatedNotification.staffId,
              updatedNotification.actualCheckoutTime,
              `Early checkout approved by admin. Reason: ${updatedNotification.reason || 'No reason provided'}`
            );

            toast({
              title: "Request Approved! ✅",
              description: `${updatedNotification.staffName} has been checked out at the requested time. Total hours: ${checkoutRecord.totalHours}h`,
            });
          } catch (attendanceError) {
            // If attendance update fails, still show success for notification approval
            console.error('Failed to update attendance:', attendanceError);
            toast({
              title: "Request Approved! ⚠️",
              description: `Early checkout request approved, but attendance update failed: ${attendanceError instanceof Error ? attendanceError.message : 'Unknown error'}`,
            });
          }
        } else {
          toast({
            title: "Request Rejected! ❌",
            description: `Early checkout request for ${updatedNotification.staffName} has been rejected.`,
          });
        }

        setSelectedNotification(null);
        setAdminComments('');
        loadData();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update notification',
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadData}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2 mb-2">
              <Bell className="h-6 w-6 text-blue-600" />
              Staff Notifications
            </h1>
            <p className="text-gray-600">Manage early checkout requests and approvals</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <Card className="border-blue-200 bg-blue-50/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-600 font-medium">Total</p>
                  <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
                </div>
                <Bell className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-yellow-200 bg-yellow-50/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-yellow-600 font-medium">Pending</p>
                  <p className="text-2xl font-bold text-yellow-900">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-green-600 font-medium">Approved</p>
                  <p className="text-2xl font-bold text-green-900">{stats.approved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-red-200 bg-red-50/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-red-600 font-medium">Rejected</p>
                  <p className="text-2xl font-bold text-red-900">{stats.rejected}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 bg-purple-50/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-purple-600 font-medium">Today</p>
                  <p className="text-2xl font-bold text-purple-900">{stats.todayRequests}</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 mb-6">
          {(['all', 'pending', 'approved', 'rejected'] as const).map((filter) => (
            <Button
              key={filter}
              variant={selectedFilter === filter ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedFilter(filter)}
              className="capitalize"
            >
              {filter}
              {filter !== 'all' && (
                <Badge variant="secondary" className="ml-2">
                  {filter === 'pending' ? stats.pending : 
                   filter === 'approved' ? stats.approved : stats.rejected}
                </Badge>
              )}
            </Button>
          ))}
        </div>

        {/* Notifications List */}
        <div className="grid gap-4">
          {filteredNotifications.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Notifications</h3>
                <p className="text-gray-600">
                  {selectedFilter === 'all' 
                    ? 'No early checkout requests found.' 
                    : `No ${selectedFilter} requests found.`}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredNotifications.map((notification) => (
              <Card key={notification.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <User className="h-5 w-5 text-gray-600" />
                        <div>
                          <h3 className="font-semibold text-gray-900">{notification.staffName}</h3>
                          <p className="text-sm text-gray-600">{notification.staffPhone}</p>
                        </div>
                        {getStatusBadge(notification.status)}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="text-sm font-medium">Early by</p>
                            <p className="text-sm text-gray-600">{notification.minutesEarly} minutes</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="text-sm font-medium">Requested</p>
                            <p className="text-sm text-gray-600">{getTimeAgo(notification.requestTime)}</p>
                          </div>
                        </div>
                        
                        {notification.reason && (
                          <div className="flex items-center gap-2">
                            <MessageSquare className="h-4 w-4 text-gray-500" />
                            <div>
                              <p className="text-sm font-medium">Reason</p>
                              <p className="text-sm text-gray-600">{notification.reason}</p>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {notification.adminResponse && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm font-medium text-gray-900">Admin Response:</p>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.adminResponse.comments || 'No comments provided'}
                          </p>
                          <p className="text-xs text-gray-500 mt-2">
                            By {notification.adminResponse.adminName} • {formatNotificationTime(notification.adminResponse.responseTime)}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    {notification.status === 'pending' && (
                      <div className="ml-4">
                        <Button
                          size="sm"
                          onClick={() => setSelectedNotification(notification)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          Review
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Review Modal */}
        {selectedNotification && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  Review Early Checkout Request
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">{selectedNotification.staffName}</p>
                  <p className="text-sm text-gray-600">Requesting to checkout {selectedNotification.minutesEarly} minutes early</p>
                  {selectedNotification.reason && (
                    <p className="text-sm text-gray-600 mt-2">
                      <strong>Reason:</strong> {selectedNotification.reason}
                    </p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Admin Comments (Optional)</label>
                  <Textarea
                    value={adminComments}
                    onChange={(e) => setAdminComments(e.target.value)}
                    placeholder="Add any comments for the staff member..."
                    rows={3}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleStatusUpdate(selectedNotification.id, 'approved')}
                    disabled={isProcessing}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve
                  </Button>
                  <Button
                    onClick={() => handleStatusUpdate(selectedNotification.id, 'rejected')}
                    disabled={isProcessing}
                    variant="destructive"
                    className="flex-1"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                </div>
                
                <Button
                  onClick={() => {
                    setSelectedNotification(null);
                    setAdminComments('');
                  }}
                  variant="outline"
                  className="w-full"
                  disabled={isProcessing}
                >
                  Cancel
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default Notifications;
