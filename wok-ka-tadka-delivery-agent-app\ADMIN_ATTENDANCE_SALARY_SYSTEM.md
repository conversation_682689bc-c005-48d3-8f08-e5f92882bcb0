# Admin Attendance & Salary Management System

## Overview
The admin system provides comprehensive attendance tracking and salary management functionality, allowing administrators to monitor staff attendance, generate salary records based on attendance data, and process salary payments.

## Features

### 1. **Attendance Tracking** (`/admin/attendance`)
- **Real-time Monitoring**: View current attendance status of all staff
- **Historical Records**: Access complete attendance history with filtering options
- **Statistics Dashboard**: Today's attendance summary with present/absent counts
- **Advanced Filtering**: Filter by date, staff member, and attendance status
- **Staff Details**: Quick access to individual staff attendance details

### 2. **Salary Management** (`/admin/salary-management`)
- **Salary Overview**: View all salary records with payment status
- **Payment Processing**: Make full or partial salary payments
- **Payment Methods**: Support for cash, bank transfer, UPI, and cheque
- **Payment History**: Track all payment transactions
- **Status Management**: Automatic status updates (pending/partial/paid)

### 3. **Salary Generation** (`/admin/generate-salary`)
- **Automated Calculation**: Generate salaries based on attendance data
- **Customizable Parameters**: Adjust working days, bonuses, and deductions
- **Role-based Base Salaries**: Different salary structures for different roles
- **Overtime Calculation**: Automatic overtime pay calculation
- **Bulk Generation**: Generate salaries for all staff at once

## Technical Implementation

### Files Created

#### 1. **src/utils/salaryStorage.ts**
- Core salary management utility
- Handles salary record creation and payment processing
- Provides calculation functions for salary components
- Manages payment history and status tracking

#### 2. **src/pages/admin/AttendanceTracking.tsx**
- Admin attendance monitoring dashboard
- Real-time attendance statistics
- Advanced filtering and search capabilities
- Staff attendance overview

#### 3. **src/pages/admin/SalaryManagement.tsx**
- Salary records management interface
- Payment processing functionality
- Salary status tracking
- Payment history management

#### 4. **src/pages/admin/SalaryGeneration.tsx**
- Automated salary generation based on attendance
- Customizable salary parameters
- Bulk salary record creation
- Preview and adjustment capabilities

#### 5. **src/utils/initializeSampleData.ts**
- Sample data generation for testing
- Creates sample staff and attendance records
- Utility functions for data management

### Data Structures

#### Salary Record
```typescript
interface SalaryRecord {
  id: string;
  staffId: string;
  staffName: string;
  staffPhone: string;
  staffRole: string;
  month: string; // YYYY-MM format
  year: number;
  baseSalary: number;
  workingDays: number;
  presentDays: number;
  totalHours: number;
  overtimeHours: number;
  overtimeRate: number;
  bonuses: number;
  deductions: number;
  finalSalary: number;
  status: 'pending' | 'paid' | 'partial';
  paidAmount: number;
  paymentDate?: string;
  paymentMethod?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
```

#### Salary Payment
```typescript
interface SalaryPayment {
  id: string;
  salaryRecordId: string;
  staffId: string;
  amount: number;
  paymentDate: string;
  paymentMethod: 'cash' | 'bank_transfer' | 'upi' | 'cheque';
  transactionId?: string;
  notes?: string;
  paidBy: string;
  createdAt: string;
}
```

### Key Functions

#### Salary Management
- `createSalaryRecord()`: Create salary record based on attendance
- `makeSalaryPayment()`: Process salary payment
- `calculateSalary()`: Calculate final salary amount
- `getSalarySummary()`: Get salary statistics for dashboard

#### Attendance Integration
- `getStaffAttendanceStats()`: Get attendance statistics for salary calculation
- `getAllAttendanceRecords()`: Retrieve attendance data for analysis

## Admin Dashboard Integration

### New Quick Actions Added:
1. **Attendance Tracking**: Monitor staff attendance in real-time
2. **Salary Management**: Manage salary records and payments

### Navigation Routes:
- `/admin/attendance` - Attendance tracking dashboard
- `/admin/salary-management` - Salary management interface
- `/admin/generate-salary` - Salary generation tool

## Usage Guide

### For Administrators

#### 1. **Monitoring Attendance**
1. Navigate to Admin Dashboard
2. Click "Attendance" in Quick Actions
3. View today's attendance statistics
4. Use filters to search specific records
5. Click "View Details" for individual staff analysis

#### 2. **Generating Salaries**
1. Go to Salary Management
2. Click "Generate Salaries"
3. Select month and set working days
4. Review and adjust individual salary components
5. Click "Generate Salaries" to create records

#### 3. **Processing Payments**
1. In Salary Management, find pending salary records
2. Click "Pay" button for the staff member
3. Enter payment amount and method
4. Add transaction ID if applicable
5. Confirm payment to update records

#### 4. **Viewing Reports**
- **Attendance Summary**: Real-time attendance statistics
- **Salary Overview**: Monthly salary expenditure
- **Payment History**: Track all salary payments
- **Staff Performance**: Attendance-based performance metrics

## Salary Calculation Logic

### Base Calculation
```
Daily Salary = Base Salary / Working Days
Base Pay = Daily Salary × Present Days
```

### Overtime Calculation
```
Hourly Rate = Base Salary / (Working Days × 8 hours)
Overtime Pay = Hourly Rate × Overtime Hours × 1.5
```

### Final Salary
```
Final Salary = Base Pay + Overtime Pay + Bonuses - Deductions
```

### Role-based Base Salaries
- **Waiter**: ₹18,000/month
- **Delivery**: ₹20,000/month
- **Kitchen**: ₹22,000/month
- **Manager**: ₹35,000/month

## Data Storage

### Storage Keys
- `wok_ka_tadka_salary_records`: Salary records
- `wok_ka_tadka_salary_payments`: Payment history
- `wok_ka_tadka_attendance`: Attendance records (shared with waiter system)

### Data Persistence
- All data stored in browser localStorage
- Automatic backup and restore capabilities
- Export functionality for external processing

## Security Features

- **Admin Authentication**: Requires admin login
- **Payment Tracking**: Complete audit trail for all payments
- **Data Validation**: Input validation for all financial data
- **Transaction IDs**: Support for external transaction references

## Mobile Responsiveness

- Fully responsive design for all screen sizes
- Touch-friendly interface for mobile devices
- Optimized layouts for tablets and phones
- Proper spacing to prevent UI overlap

## Integration Points

### With Existing Systems
- **Staff PIN System**: Uses existing staff authentication
- **Attendance System**: Integrates with waiter attendance tracking
- **Admin Dashboard**: Seamlessly integrated with existing admin interface

### Future Enhancements
- **Payroll Integration**: Connect with external payroll systems
- **Bank API Integration**: Direct bank transfers
- **Report Generation**: PDF/Excel export capabilities
- **Email Notifications**: Automated payment confirmations
- **Tax Calculations**: Automatic tax deductions
- **Performance Analytics**: Advanced staff performance metrics

## Testing

### Sample Data
The system includes sample data generation for testing:
```typescript
import { initializeAllSampleData, clearAllSampleData } from '@/utils/initializeSampleData';

// Initialize sample data
initializeAllSampleData();

// Clear sample data
clearAllSampleData();
```

### Test Scenarios
1. **Attendance Tracking**: View and filter attendance records
2. **Salary Generation**: Create salary records for multiple staff
3. **Payment Processing**: Make full and partial payments
4. **Data Validation**: Test input validation and error handling

## Performance Considerations

- **Efficient Filtering**: Optimized search and filter operations
- **Lazy Loading**: Load data on demand for better performance
- **Local Storage**: Fast data access with browser storage
- **Minimal Re-renders**: Optimized React component updates

## Browser Compatibility

- Works in all modern browsers
- Requires localStorage support
- Mobile browser compatible
- Progressive Web App ready

This comprehensive system provides administrators with complete control over staff attendance monitoring and salary management, streamlining restaurant operations and ensuring accurate payroll processing.
