// Mobile-optimized thermal printer service for Everycom EC58
// Supports both web app and mobile app environments

import { gstSettingsManager } from '../utils/gstSettings';
import { restaurantSettingsManager } from '../utils/restaurantSettings';

interface MobilePrinterConfig {
  type: 'bluetooth' | 'usb' | 'wifi';
  deviceName?: string;
  address?: string;
  port?: number;
}

export class MobileThermalPrinterService {
  private config: MobilePrinterConfig;
  private isConnected: boolean = false;
  private bluetoothDevice: BluetoothDevice | null = null;
  private bluetoothCharacteristic: BluetoothRemoteGATTCharacteristic | null = null;

  constructor(config: MobilePrinterConfig) {
    this.config = config;
  }

  // Check if we're running in a mobile environment
  private isMobileEnvironment(): boolean {
    return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  // Enhanced mobile Bluetooth connection for Everycom EC58
  async connectBluetooth(): Promise<boolean> {
    try {
      if (!navigator.bluetooth) {
        throw new Error('Bluetooth not supported in this browser/environment');
      }

      console.log('Requesting Bluetooth device for mobile...');

      // Enhanced mobile-optimized device request for Everycom EC58
      const device = await navigator.bluetooth.requestDevice({
        filters: [
          // Everycom EC58 specific patterns (most likely to work)
          { namePrefix: 'EC58' },
          { namePrefix: 'Everycom' },
          { namePrefix: 'EC-58' },
          { namePrefix: 'EVERYCOM' },
          { namePrefix: 'EC_58' },
          { namePrefix: 'BT58' },
          // Generic thermal printer patterns for mobile compatibility
          { namePrefix: 'Thermal' },
          { namePrefix: 'POS' },
          { namePrefix: 'BT' },
          { namePrefix: 'Printer' },
          { namePrefix: 'Receipt' },
          { namePrefix: 'MTP' }, // Mobile Thermal Printer
          { namePrefix: '58mm' },
          // Service-based filters for better mobile compatibility
          { services: ['000018f0-0000-1000-8000-00805f9b34fb'] }, // Nordic UART
          { services: ['00001101-0000-1000-8000-00805f9b34fb'] }, // Serial Port Profile (SPP)
          { services: ['0000ffe0-0000-1000-8000-00805f9b34fb'] }, // Common thermal printer service
        ],
        optionalServices: [
          // Standard Bluetooth services for thermal printers
          '000018f0-0000-1000-8000-00805f9b34fb', // Nordic UART Service
          '00001101-0000-1000-8000-00805f9b34fb', // Serial Port Profile
          '0000180f-0000-1000-8000-00805f9b34fb', // Battery service
          '00001800-0000-1000-8000-00805f9b34fb', // Generic access
          '00001801-0000-1000-8000-00805f9b34fb', // Generic attribute
          // Thermal printer specific services
          '49535343-fe7d-4ae5-8fa9-9fafd205e455', // Microchip service
          '6e400001-b5a3-f393-e0a9-e50e24dcca9e', // Nordic UART alternative
          '0000ffe0-0000-1000-8000-00805f9b34fb', // Common thermal printer service
          // Everycom specific (if any)
          '00002af1-0000-1000-8000-00805f9b34fb', // Write characteristic
          '00002af0-0000-1000-8000-00805f9b34fb', // Read characteristic
        ]
      });

      console.log('Mobile Bluetooth device found:', device.name, 'ID:', device.id);
      this.bluetoothDevice = device;

      // Add disconnect listener
      device.addEventListener('gattserverdisconnected', () => {
        console.log('Bluetooth device disconnected');
        this.isConnected = false;
        this.bluetoothCharacteristic = null;
      });

      const server = await device.gatt?.connect();
      if (!server) {
        throw new Error('Failed to connect to GATT server');
      }

      console.log('Connected to GATT server, discovering services...');

      // Try multiple service UUIDs commonly used by thermal printers
      const serviceUUIDs = [
        '000018f0-0000-1000-8000-00805f9b34fb', // Nordic UART
        '00001101-0000-1000-8000-00805f9b34fb', // Serial Port Profile
        '49535343-fe7d-4ae5-8fa9-9fafd205e455', // Microchip
        '6e400001-b5a3-f393-e0a9-e50e24dcca9e', // Nordic UART alternative
        '0000ffe0-0000-1000-8000-00805f9b34fb', // Common thermal printer
      ];

      let service = null;
      let characteristic = null;

      for (const serviceUUID of serviceUUIDs) {
        try {
          service = await server.getPrimaryService(serviceUUID);
          console.log(`Found service: ${serviceUUID}`);
          
          // Try different write characteristic UUIDs
          const writeCharUUIDs = [
            '00002af1-0000-1000-8000-00805f9b34fb', // Nordic write
            '49535343-1e4d-4bd9-ba61-23c647249616', // Microchip write
            '6e400002-b5a3-f393-e0a9-e50e24dcca9e', // Nordic write alternative
            '0000ffe1-0000-1000-8000-00805f9b34fb', // Common thermal printer write
            '0000fff1-0000-1000-8000-00805f9b34fb', // Alternative write
          ];

          for (const charUUID of writeCharUUIDs) {
            try {
              characteristic = await service.getCharacteristic(charUUID);
              console.log(`Found write characteristic: ${charUUID}`);
              break;
            } catch (e) {
              console.log(`Characteristic ${charUUID} not found, trying next...`);
            }
          }
          
          if (characteristic) break;
        } catch (e) {
          console.log(`Service ${serviceUUID} not found, trying next...`);
        }
      }

      if (characteristic) {
        this.bluetoothCharacteristic = characteristic;
        console.log('Bluetooth characteristic configured for mobile printing');
      } else {
        console.warn('No write characteristic found, will try basic connection');
      }

      this.isConnected = true;
      console.log('Mobile Bluetooth printer connected successfully');
      return true;

    } catch (error) {
      console.error('Mobile Bluetooth connection failed:', error);

      // Mobile-specific error messages
      if (error.name === 'NotFoundError') {
        console.log('No device selected. Make sure your Everycom EC58 is in pairing mode (usually hold power button).');
      } else if (error.name === 'SecurityError') {
        console.log('Bluetooth access denied. Make sure you\'re using HTTPS and have granted Bluetooth permissions.');
      } else if (error.name === 'NetworkError') {
        console.log('Connection failed. Make sure the printer is powered on, in pairing mode, and not connected to another device.');
      } else if (error.name === 'NotSupportedError') {
        console.log('Bluetooth not supported. On mobile, make sure Bluetooth is enabled in device settings.');
      }

      return false;
    }
  }

  // Print KOT using mobile thermal printer
  async printKOT(kot: any): Promise<boolean> {
    try {
      const restaurantInfo = restaurantSettingsManager.getFormattedHeader();
      const now = new Date(kot.createdAt || new Date());
      const date = now.toLocaleDateString('en-IN');
      const time = now.toLocaleTimeString('en-IN');

      let content = '';
      const separator = '------------------------------';

      // Header
      content += `${restaurantInfo.name}\n`;
      content += `${restaurantInfo.address}\n`;
      content += `Ph: ${restaurantInfo.phone}\n`;
      content += separator + '\n';
      content += `KITCHEN ORDER TICKET\n`;
      content += separator + '\n';

      // KOT Details
      content += `KOT No: ${kot.kotNumber}\n`;
      content += `Table: ${kot.tableId}\n`;
      content += `Date: ${date}\n`;
      content += `Time: ${time}\n`;
      content += separator + '\n';

      // Items
      content += `ITEMS:\n`;
      content += separator + '\n';
      kot.items.forEach((item: any) => {
        content += `${item.name}\n`;
        content += `Qty: ${item.quantity}  Price: ₹${item.price}\n`;
        if (item.specialInstructions) {
          content += `Note: ${item.specialInstructions}\n`;
        }
        content += '\n';
      });

      content += separator + '\n';
      content += `Total Items: ${kot.items.length}\n`;
      content += separator + '\n';
      content += `Kitchen Copy\n`;
      content += `Please prepare the above items\n`;

      return await this.printContent(content, 'kot');
    } catch (error) {
      console.error('Mobile KOT print failed:', error);
      return false;
    }
  }

  // Mobile-optimized printing
  async printContent(content: string, type: 'kot' | 'bill' = 'kot'): Promise<boolean> {
    try {
      if (!this.isConnected || !this.bluetoothCharacteristic) {
        console.log('Printer not connected, attempting to connect...');
        const connected = await this.connectBluetooth();
        if (!connected) {
          throw new Error('Failed to connect to printer');
        }
      }

      console.log('Preparing content for mobile thermal printing...');

      // Generate mobile-optimized ESC/POS commands
      const commands = this.generateMobileESCPOS(content, type);

      // Send to printer with mobile-optimized chunking
      return await this.sendToMobilePrinter(commands);

    } catch (error) {
      console.error('Mobile print failed:', error);
      return false;
    }
  }

  // Print Bill (for compatibility with PrintService)
  async printBill(billData: any): Promise<boolean> {
    // Professional bill format with restaurant information at the top
    let content = '';

    // Get GST settings instead of using hardcoded gstPercent
    const gstSettings = gstSettingsManager.getGSTSettings();

    const separator = '------------------------------';
    const now = new Date();
    const date = now.toLocaleDateString('en-IN');
    const time = now.toLocaleTimeString('en-IN');

    if (billData && billData.kot) {
      // Header with restaurant information
      const restaurantInfo = restaurantSettingsManager.getFormattedHeader();
      content += `${restaurantInfo.name}\n`;
      content += `${restaurantInfo.address}\n`;
      content += `Ph: ${restaurantInfo.phone}\n`;
      content += `GST: ${restaurantSettingsManager.getGSTNumber()}\n`;
      content += separator + '\n';
      content += `BILL RECEIPT\n`;
      content += separator + '\n';
      // Bill details
      content += `Bill No: ${billData.kot.kotNumber}\n`;
      content += `Table: ${billData.kot.tableId}\n`;
      content += `Date: ${date}  Time: ${time}\n`;
      if (billData.customer && billData.customer.name) {
        content += `Customer: ${billData.customer.name}\n`;
      }
      content += separator + '\n';
      // Items header
      content += `Item         Qty   Price   Amt\n`;
      content += separator + '\n';
      // Items
      let subtotal = 0;
      billData.kot.items.forEach((item: any) => {
        const amt = item.price * item.quantity;
        subtotal += amt;
        // Format: ItemName   Qty   Price   Amt
        const name = item.name.length > 10 ? item.name.slice(0,10) : item.name.padEnd(10);
        const qty = String(item.quantity).padStart(3);
        const price = String(item.price).padStart(6);
        const amount = String(amt).padStart(6);
        content += `${name} ${qty} ${price} ${amount}\n`;
      });
      content += separator + '\n';
      // Totals
      content += `Subtotal:         ₹${subtotal.toFixed(2)}\n`;
      let discount = billData.discount || 0;
      if (discount > 0) {
        content += `Discount:         -₹${discount.toFixed(2)}\n`;
        subtotal -= discount;
      }
      let cgstAmount = 0;
      let sgstAmount = 0;
      let gstAmount = 0;
      if (billData.includeGST) {
        cgstAmount = (subtotal * gstSettings.cgstRate) / 100;
        sgstAmount = (subtotal * gstSettings.sgstRate) / 100;
        gstAmount = cgstAmount + sgstAmount;
        content += `CGST (${gstSettings.cgstRate}%): ₹${cgstAmount.toFixed(2)}\n`;
        content += `SGST (${gstSettings.sgstRate}%): ₹${sgstAmount.toFixed(2)}\n`;
      }
      const grandTotal = subtotal + gstAmount;
      content += `Grand Total:      ₹${grandTotal.toFixed(2)}\n`;
      content += separator + '\n';
      content += `Payment: ${billData.paymentMethod ? billData.paymentMethod.toUpperCase() : 'CASH'}\n`;
      content += separator + '\n';
      content += `Thank you for dining with us!\n`;
    } else {
      content = 'No bill data.';
    }
    return this.printContent(content, 'bill');
  }

  // Generate mobile-optimized ESC/POS commands
  private generateMobileESCPOS(content: string, type: string): Uint8Array {
    const commands: number[] = [];

    // Initialize printer
    commands.push(0x1B, 0x40); // ESC @ - Initialize

    // Set character set to UTF-8 compatible
    commands.push(0x1B, 0x74, 0x00); // ESC t 0 - Character set

    // Set line spacing
    commands.push(0x1B, 0x33, 0x20); // ESC 3 32 - Set line spacing

    // Center align for header
    commands.push(0x1B, 0x61, 0x01); // ESC a 1 - Center align

    // Bold on
    commands.push(0x1B, 0x45, 0x01); // ESC E 1 - Bold on

    // Add header
    const header = type === 'kot' ? 'KITCHEN ORDER' : 'BILL RECEIPT';
    commands.push(...this.stringToBytes(header + '\n'));

    // Bold off
    commands.push(0x1B, 0x45, 0x00); // ESC E 0 - Bold off

    // Left align for content
    commands.push(0x1B, 0x61, 0x00); // ESC a 0 - Left align

    // Add content
    commands.push(...this.stringToBytes(content));

    // Feed and cut
    commands.push(0x0A, 0x0A, 0x0A); // Line feeds
    commands.push(0x1D, 0x56, 0x42, 0x00); // Partial cut

    return new Uint8Array(commands);
  }

  // Convert string to bytes for mobile printing
  private stringToBytes(str: string): number[] {
    const bytes: number[] = [];
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (code < 128) {
        bytes.push(code);
      } else {
        // Handle Unicode characters for mobile
        bytes.push(0x3F); // Replace with '?' for compatibility
      }
    }
    return bytes;
  }

  // Send data to mobile printer with optimized chunking
  private async sendToMobilePrinter(commands: Uint8Array): Promise<boolean> {
    try {
      if (!this.bluetoothCharacteristic) {
        throw new Error('Bluetooth characteristic not available');
      }

      console.log('Sending data to mobile thermal printer...');

      // Mobile-optimized chunk size (smaller for better compatibility)
      const chunkSize = this.isMobileEnvironment() ? 16 : 20;
      const chunks = [];
      
      for (let i = 0; i < commands.length; i += chunkSize) {
        chunks.push(commands.slice(i, i + chunkSize));
      }

      console.log(`Sending ${chunks.length} chunks to mobile printer`);

      // Send each chunk with mobile-optimized timing
      for (let i = 0; i < chunks.length; i++) {
        try {
          await this.bluetoothCharacteristic.writeValue(chunks[i]);
          console.log(`Mobile: Sent chunk ${i + 1}/${chunks.length}`);
          
          // Longer delay for mobile to ensure reliability
          if (i < chunks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        } catch (error) {
          console.error(`Failed to send mobile chunk ${i + 1}:`, error);
          throw error;
        }
      }

      // Wait for mobile printing to complete
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Mobile thermal print completed successfully');
      return true;

    } catch (error) {
      console.error('Mobile thermal print failed:', error);
      return false;
    }
  }

  // Check connection status
  isConnectedToPrinter(): boolean {
    return this.isConnected && this.bluetoothCharacteristic !== null;
  }

  // Disconnect from printer
  async disconnect(): Promise<void> {
    try {
      if (this.bluetoothDevice && this.bluetoothDevice.gatt?.connected) {
        await this.bluetoothDevice.gatt.disconnect();
      }
      this.isConnected = false;
      this.bluetoothCharacteristic = null;
      this.bluetoothDevice = null;
      console.log('Mobile printer disconnected');
    } catch (error) {
      console.error('Error disconnecting mobile printer:', error);
    }
  }
}

// Global mobile printer instance
let globalMobilePrinter: MobileThermalPrinterService | null = null;

// Exported function to check if mobile printer is available (for import in printService)
export function isMobilePrinterAvailable(): boolean {
  // You can enhance this logic as needed
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

export function getGlobalMobilePrinter(): MobileThermalPrinterService {
  if (!globalMobilePrinter) {
    globalMobilePrinter = new MobileThermalPrinterService({
      type: 'bluetooth'
    });
  }
  return globalMobilePrinter;
}
