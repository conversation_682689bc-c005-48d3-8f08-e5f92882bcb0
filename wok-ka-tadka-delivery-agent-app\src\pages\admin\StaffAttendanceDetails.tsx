import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Clock, 
  Calendar,
  CheckCircle,
  XCircle,
  Timer,
  TrendingUp,
  User
} from "lucide-react";
import {
  getStaffAttendanceRecords,
  getStaffAttendanceStats,
  formatTime,
  formatDate,
  type AttendanceRecord
} from "@/utils/attendanceStorage";
import { getStaffById } from "@/utils/staffPinStorage";

const StaffAttendanceDetails = () => {
  const navigate = useNavigate();
  const { staffId } = useParams<{ staffId: string }>();
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [stats, setStats] = useState({
    totalDays: 0,
    presentDays: 0,
    absentDays: 0,
    averageHours: 0,
    totalHours: 0
  });
  const [selectedPeriod, setSelectedPeriod] = useState(30);
  const [staffInfo, setStaffInfo] = useState<any>(null);

  useEffect(() => {
    if (staffId) {
      loadAttendanceData();
      loadStaffInfo();
    }
  }, [staffId, selectedPeriod]);

  const loadAttendanceData = () => {
    if (!staffId) return;
    
    // Load attendance records
    const records = getStaffAttendanceRecords(staffId, 50); // Get last 50 records
    setAttendanceRecords(records);

    // Load statistics
    const attendanceStats = getStaffAttendanceStats(staffId, selectedPeriod);
    setStats(attendanceStats);
  };

  const loadStaffInfo = () => {
    if (!staffId) return;
    
    const staff = getStaffById(staffId);
    setStaffInfo(staff);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'checked-in':
        return 'bg-blue-100 text-blue-800';
      case 'checked-out':
        return 'bg-green-100 text-green-800';
      case 'absent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'checked-in':
        return <Clock className="h-4 w-4" />;
      case 'checked-out':
        return <CheckCircle className="h-4 w-4" />;
      case 'absent':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Timer className="h-4 w-4" />;
    }
  };

  if (!staffId) {
    return (
      <div className="apk-page-container bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Staff ID not provided</p>
        </div>
      </div>
    );
  }

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/attendance")}
              className="text-white hover:bg-white/20 transition-all duration-200 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-white truncate">
                {staffInfo ? `${staffInfo.name}'s Attendance` : 'Staff Attendance'}
              </h1>
              <p className="text-white/80 text-xs sm:text-sm truncate">Detailed attendance records</p>
            </div>
          </div>
          <div className="flex items-center gap-2 shrink-0">
            <User className="h-5 w-5 text-white/80" />
            <span className="text-white text-sm font-medium capitalize">
              {staffInfo?.role || 'Staff'}
            </span>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Staff Info Card */}
        {staffInfo && (
          <Card className="shadow-sm border-0 bg-white">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="bg-primary/10 p-3 rounded-full">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{staffInfo.name}</h3>
                  <p className="text-gray-600 capitalize">{staffInfo.role}</p>
                  <p className="text-sm text-gray-500">{staffInfo.phone}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Staff ID</p>
                  <p className="font-medium">{staffInfo.id}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Period Selection */}
        <div className="flex gap-2 overflow-x-auto pb-2">
          {[7, 15, 30, 60].map((days) => (
            <Button
              key={days}
              variant={selectedPeriod === days ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedPeriod(days)}
              className="whitespace-nowrap"
            >
              Last {days} days
            </Button>
          ))}
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.presentDays}</p>
                <p className="text-sm text-gray-500">Present Days</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-red-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.absentDays}</p>
                <p className="text-sm text-gray-500">Absent Days</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-blue-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Timer className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.totalHours}h</p>
                <p className="text-sm text-gray-500">Total Hours</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-amber-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-amber-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.averageHours}h</p>
                <p className="text-sm text-gray-500">Avg Hours/Day</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Records */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Attendance History ({attendanceRecords.length} records)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {attendanceRecords.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No attendance records found</p>
                <p className="text-sm text-gray-400">This staff member hasn't marked attendance yet</p>
              </div>
            ) : (
              attendanceRecords.map((record) => (
                <div
                  key={record.id}
                  className="p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2 flex-wrap">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(record.status)}
                          <span className="font-semibold text-gray-900">
                            {formatDate(record.date)}
                          </span>
                        </div>
                        <Badge className={`${getStatusColor(record.status)} text-xs px-2 py-1`}>
                          {record.status.replace('-', ' ').toUpperCase()}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Check In:</span>
                          <span className="font-medium ml-2">
                            {record.checkInTime ? formatTime(record.checkInTime) : 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Check Out:</span>
                          <span className="font-medium ml-2">
                            {record.checkOutTime ? formatTime(record.checkOutTime) : 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Total Hours:</span>
                          <span className="font-medium ml-2">
                            {record.totalHours ? `${record.totalHours}h` : 'N/A'}
                          </span>
                        </div>
                      </div>
                      
                      {record.notes && (
                        <div className="mt-2 text-sm">
                          <span className="text-gray-500">Notes:</span>
                          <span className="ml-2 text-gray-700">{record.notes}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StaffAttendanceDetails;
