import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { gstSettingsManager, GSTSettings } from "@/utils/gstSettings";
import {
  ArrowLeft,
  Search,
  Calculator,
  Printer,
  Download,
  RefreshCw,
  Receipt,
  CreditCard,
  Banknote,
  Smartphone,
  Percent,
  Plus,
  Minus,
  Eye
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import CustomerSelectionDialog from "@/components/CustomerSelectionDialog";
import PaymentMethodDialog, { PaymentMethod } from "@/components/PaymentMethodDialog";
import { Customer, customerManager } from "@/utils/customerStorage";
import PrintService from "@/services/printService";

const BillGeneration = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false);
  const [discountType, setDiscountType] = useState<"percentage" | "amount">("percentage");
  const [discountValue, setDiscountValue] = useState("");
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("cash");
  const [customerNotes, setCustomerNotes] = useState("");
  const [includeGST, setIncludeGST] = useState(true); // GST toggle state
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showPaymentMethodDialog, setShowPaymentMethodDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('cash');
  const [gstSettings, setGSTSettings] = useState<GSTSettings>(
    gstSettingsManager.getGSTSettings()
  );

  // Listen for GST settings changes
  useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent<GSTSettings>) => {
      setGSTSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);

    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    };
  }, []);



  // Mock orders ready for billing
  const ordersForBilling = [
    {
      id: "#ORD001",
      tableNumber: 3,
      customerName: "Raj Kumar",
      phone: "+91 98765 43210",
      waiter: "Sunita Devi",
      orderTime: "12:30 PM",
      items: [
        { id: 1, name: "Chicken Biryani", quantity: 2, price: 300, total: 600 },
        { id: 2, name: "Paneer Butter Masala", quantity: 1, price: 250, total: 250 },
        { id: 3, name: "Garlic Naan", quantity: 3, price: 60, total: 180 },
        { id: 4, name: "Lassi", quantity: 2, price: 80, total: 160 }
      ],
      subtotal: 1190,
      status: "ready_for_billing",
      type: "dine-in"
    },
    {
      id: "#ORD002",
      tableNumber: 7,
      customerName: "Priya Sharma",
      phone: "+91 87654 32109",
      waiter: "Raj Kumar",
      orderTime: "12:45 PM",
      items: [
        { id: 1, name: "Mutton Curry", quantity: 1, price: 400, total: 400 },
        { id: 2, name: "Jeera Rice", quantity: 2, price: 140, total: 280 },
        { id: 3, name: "Raita", quantity: 1, price: 60, total: 60 }
      ],
      subtotal: 740,
      status: "ready_for_billing",
      type: "dine-in"
    },
    {
      id: "#ORD003",
      customerName: "Rohit Gupta",
      phone: "+91 76543 21098",
      address: "Sector 18, Noida",
      deliveryAgent: "Amit Singh",
      orderTime: "1:15 PM",
      items: [
        { id: 1, name: "Dal Makhani", quantity: 2, price: 200, total: 400 },
        { id: 2, name: "Butter Naan", quantity: 4, price: 50, total: 200 },
        { id: 3, name: "Gulab Jamun", quantity: 1, price: 120, total: 120 }
      ],
      subtotal: 720,
      deliveryCharges: 50,
      status: "ready_for_billing",
      type: "delivery"
    }
  ];

  // Tax configuration
  const taxConfig = {
    cgst: 2.5, // %
    sgst: 2.5, // %
    serviceCharge: 10, // %
    packagingCharges: 20 // flat amount for delivery
  };

  const calculateBill = (order: any, discount: number = 0, discountType: "percentage" | "amount" = "percentage", includeGST: boolean = true) => {
    const subtotal = order.subtotal;
    const deliveryCharges = order.deliveryCharges || 0;
    const packagingCharges = order.type === "delivery" ? taxConfig.packagingCharges : 0;

    // Calculate discount
    let discountAmount = 0;
    if (discount > 0) {
      discountAmount = discountType === "percentage"
        ? (subtotal * discount) / 100
        : discount;
    }

    const discountedSubtotal = subtotal - discountAmount;

    // Calculate taxes on discounted amount using global GST settings
    const cgst = includeGST ? (discountedSubtotal * gstSettings.cgstRate) / 100 : 0;
    const sgst = includeGST ? (discountedSubtotal * gstSettings.sgstRate) / 100 : 0;

    const totalTax = cgst + sgst;
    const grandTotal = discountedSubtotal + totalTax + deliveryCharges + packagingCharges;

    return {
      subtotal,
      discountAmount,
      discountedSubtotal,
      cgst,
      sgst,
      deliveryCharges,
      packagingCharges,
      totalTax,
      grandTotal,
      includeGST,
      gstRate: gstSettings.totalGSTRate
    };
  };

  const filteredOrders = ordersForBilling.filter(order =>
    order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Generate HTML content for bill printing/PDF
  const generateBillHTML = (order: any, billDetails: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Bill - ${order.id}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          line-height: 1.4;
          color: #333;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
          margin-bottom: 20px;
        }
        .restaurant-name {
          font-size: 24px;
          font-weight: bold;
          color: #d32f2f;
          margin-bottom: 5px;
        }
        .restaurant-info {
          font-size: 12px;
          color: #666;
        }
        .bill-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          font-size: 12px;
        }
        .customer-info {
          background: #f5f5f5;
          padding: 15px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        .customer-info h3 {
          margin: 0 0 10px 0;
          font-size: 16px;
          color: #333;
        }
        .customer-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          font-size: 14px;
        }
        .customer-details div {
          margin-bottom: 5px;
        }
        .customer-details strong {
          color: #666;
          font-weight: normal;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        th, td {
          padding: 8px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        .total-row {
          font-weight: bold;
          background-color: #f0f0f0;
        }
        .grand-total {
          font-size: 18px;
          background-color: #e3f2fd;
          color: #1976d2;
        }
        .payment-info {
          background: #f5f5f5;
          padding: 15px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
          font-size: 12px;
          color: #666;
        }
        .notes {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          padding: 10px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="restaurant-name">Wok Ka Tadka</div>
        <div class="restaurant-info">Mumbai Style Chinese & Indian</div>
        <div class="restaurant-info">Phone: +91 9876543210 | Email: <EMAIL></div>
      </div>

      <div class="bill-info">
        <div><strong>Bill No:</strong> ${order.id}</div>
        <div><strong>Date:</strong> ${currentDate}</div>
        <div><strong>Time:</strong> ${currentTime}</div>
      </div>

      <div class="customer-info">
        <h3>Customer Information</h3>
        <div class="customer-details">
          <div><strong>Name:</strong> ${order.customerName}</div>
          <div><strong>Phone:</strong> ${order.phone}</div>
          ${order.tableNumber ? `<div><strong>Table:</strong> Table ${order.tableNumber}</div>` : ''}
          ${order.address ? `<div><strong>Address:</strong> ${order.address}</div>` : ''}
        </div>
      </div>

      <table>
        <thead>
          <tr>
            <th>Item</th>
            <th>Qty</th>
            <th>Price</th>
            <th>Amount</th>
          </tr>
        </thead>
        <tbody>
          ${order.items.map((item: any) => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>₹${item.price}</td>
              <td>₹${item.total}</td>
            </tr>
          `).join('')}
          <tr>
            <td colspan="3"><strong>Subtotal</strong></td>
            <td><strong>₹${billDetails.subtotal.toFixed(2)}</strong></td>
          </tr>
          ${billDetails.discountAmount > 0 ? `
            <tr>
              <td colspan="3"><strong>Discount (${discountType === 'percentage' ? discountValue + '%' : '₹' + discountValue})</strong></td>
              <td><strong>-₹${billDetails.discountAmount.toFixed(2)}</strong></td>
            </tr>
          ` : ''}
          ${includeGST ? `
            <tr>
              <td colspan="3"><strong>CGST (${gstSettings.cgstRate}%)</strong></td>
              <td><strong>₹${billDetails.cgst.toFixed(2)}</strong></td>
            </tr>
            <tr>
              <td colspan="3"><strong>SGST (${gstSettings.sgstRate}%)</strong></td>
              <td><strong>₹${billDetails.sgst.toFixed(2)}</strong></td>
            </tr>
          ` : `
            <tr style="color: #f59e0b;">
              <td colspan="3"><strong>GST (Excluded)</strong></td>
              <td><strong>₹0.00</strong></td>
            </tr>
          `}
          ${billDetails.deliveryCharges > 0 ? `
            <tr>
              <td colspan="3"><strong>Delivery Charges</strong></td>
              <td><strong>₹${billDetails.deliveryCharges.toFixed(2)}</strong></td>
            </tr>
          ` : ''}
          ${billDetails.packagingCharges > 0 ? `
            <tr>
              <td colspan="3"><strong>Packaging Charges</strong></td>
              <td><strong>₹${billDetails.packagingCharges.toFixed(2)}</strong></td>
            </tr>
          ` : ''}
          <tr class="total-row grand-total">
            <td colspan="3"><strong>Grand Total ${includeGST ? '(incl. GST)' : '(excl. GST)'}</strong></td>
            <td><strong>₹${billDetails.grandTotal.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>

      <div class="payment-info">
        <strong>Payment Method:</strong> ${paymentMethod.toUpperCase()}
      </div>

      ${customerNotes ? `
        <div class="notes">
          <strong>Customer Notes:</strong><br>
          ${customerNotes}
        </div>
      ` : ''}

      <div class="footer">
        <p>Thank you for dining with us!</p>
        <p>Visit us again soon!</p>
        <p>Generated on ${currentDate} at ${currentTime}</p>
      </div>
    </body>
    </html>`;
  };

  const handleGenerateBill = (order: any) => {
    setSelectedOrder(order);
    setDiscountValue("");
    setDiscountType("percentage");
    setPaymentMethod("cash");
    setCustomerNotes("");
    setIncludeGST(true); // Default to GST enabled
    setIsBillDialogOpen(true);
  };

  const handlePrintBill = () => {
    setShowCustomerDialog(true);
  };

  const handleCustomerSelection = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setShowCustomerDialog(false);
    setShowPaymentMethodDialog(true);
  };

  const handlePaymentMethodSelection = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setShowPaymentMethodDialog(false);
    printBill(selectedCustomer, paymentMethod);
  };

  const printBill = async (customer: Customer | null, paymentMethod: PaymentMethod) => {
    if (!selectedOrder) return;

    try {
      // Convert selectedOrder to KOT format for unified print service
      const kotData = {
        kotNumber: selectedOrder.id,
        tableId: selectedOrder.tableId || 'N/A',
        items: selectedOrder.items,
        totalAmount: selectedOrder.totalAmount,
        createdAt: selectedOrder.createdAt,
        status: 'active'
      };

      const discount = parseFloat(discountValue) || 0;

      // Use unified print service
      const success = await PrintService.printBill(kotData, customer, {
        includeGST,
        discount,
        discountType,
        paymentMethod,
        customerNotes
      });

      if (success) {
        // Close the dialog after successful printing
        setIsBillDialogOpen(false);
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      console.error('Print error:', error);
      // Fallback to old browser printing method
      const discount = parseFloat(discountValue) || 0;
      const billDetails = calculateBill(selectedOrder, discount, discountType, includeGST);
      const billHTML = generateBillHTML(selectedOrder, billDetails);

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(billHTML);
        printWindow.document.close();
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
          }, 500);
        };
      }
      setIsBillDialogOpen(false);
    }
  };

  const handleDownloadBill = () => {
    if (!selectedOrder) return;

    const discount = parseFloat(discountValue) || 0;
    const billDetails = calculateBill(selectedOrder, discount, discountType, includeGST);

    // Generate HTML content for PDF
    const billHTML = generateBillHTML(selectedOrder, billDetails);

    // Create a new window for PDF generation
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(billHTML);
      printWindow.document.close();

      // Wait for content to load, then trigger print dialog which allows saving as PDF
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };
    }
  };

  const currentBill = selectedOrder ? calculateBill(
    selectedOrder,
    parseFloat(discountValue) || 0,
    discountType,
    includeGST
  ) : null;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Bill Generation</h1>
              <p className="text-sm text-gray-500">Generate and manage customer bills</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600"
            >
              <RefreshCw className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Search */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by order ID or customer name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Orders Ready for Billing */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Orders Ready for Billing</h2>
          
          {filteredOrders.map((order) => {
            const billPreview = calculateBill(order, 0, "percentage", true); // Default with GST
            
            return (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Receipt className="h-5 w-5 text-gray-600" />
                      <div>
                        <span className="font-bold text-lg">{order.id}</span>
                        <Badge className="ml-2 bg-green-100 text-green-800">
                          Ready for Billing
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-green-600">₹{billPreview.grandTotal.toFixed(2)}</p>
                      <p className="text-sm text-gray-500">Total Amount</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-500">Customer</p>
                      <p className="font-medium">{order.customerName}</p>
                      <p className="text-sm text-gray-600">{order.phone}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">
                        {order.type === "dine-in" ? "Table" : order.type === "delivery" ? "Address" : "Type"}
                      </p>
                      <p className="font-medium">
                        {order.tableNumber ? `Table ${order.tableNumber}` : 
                         order.address ? order.address : order.type}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.waiter ? `Waiter: ${order.waiter}` : 
                         order.deliveryAgent ? `Agent: ${order.deliveryAgent}` : ""}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Order Time</p>
                      <p className="font-medium">{order.orderTime}</p>
                      <p className="text-sm text-gray-600">{order.items.length} items</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-2">Items</p>
                    <div className="space-y-1">
                      {order.items.map((item: any) => (
                        <div key={item.id} className="flex justify-between text-sm bg-gray-50 p-2 rounded">
                          <span>{item.quantity}x {item.name}</span>
                          <span>₹{item.total}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                    <div>
                      <p className="text-gray-500">Subtotal</p>
                      <p className="font-medium">₹{billPreview.subtotal.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Taxes</p>
                      <p className="font-medium">₹{billPreview.totalTax.toFixed(2)}</p>
                    </div>
                    {order.deliveryCharges && (
                      <div>
                        <p className="text-gray-500">Delivery</p>
                        <p className="font-medium">₹{order.deliveryCharges.toFixed(2)}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-gray-500">Grand Total</p>
                      <p className="font-bold text-lg">₹{billPreview.grandTotal.toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleGenerateBill(order)}
                      className="flex items-center gap-1"
                    >
                      <Calculator className="h-4 w-4" />
                      Generate Bill
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredOrders.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No orders ready for billing</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Bill Generation Dialog */}
      <Dialog open={isBillDialogOpen} onOpenChange={setIsBillDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-xl font-bold text-center">Generate Bill - {selectedOrder?.id}</DialogTitle>
          </DialogHeader>
          
          {selectedOrder && currentBill && (
            <div className="space-y-6 p-2">
              {/* Customer Info */}
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                <h3 className="font-semibold mb-3 text-blue-800 flex items-center gap-2">
                  <Receipt className="h-4 w-4" />
                  Customer Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-1">
                    <p className="text-gray-600 font-medium">Name</p>
                    <p className="font-semibold text-gray-900">{selectedOrder.customerName}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-gray-600 font-medium">Phone</p>
                    <p className="font-semibold text-gray-900">{selectedOrder.phone}</p>
                  </div>
                  {selectedOrder.tableNumber && (
                    <div className="space-y-1">
                      <p className="text-gray-600 font-medium">Table</p>
                      <p className="font-semibold text-gray-900">Table {selectedOrder.tableNumber}</p>
                    </div>
                  )}
                  {selectedOrder.address && (
                    <div className="space-y-1 md:col-span-2">
                      <p className="text-gray-600 font-medium">Address</p>
                      <p className="font-semibold text-gray-900">{selectedOrder.address}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Items */}
              <div>
                <h3 className="font-semibold mb-3 text-gray-800 flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  Order Items
                </h3>
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="grid grid-cols-12 gap-2 p-3 bg-gray-100 font-semibold text-sm text-gray-700 border-b">
                    <div className="col-span-6">Item Name</div>
                    <div className="col-span-2 text-center">Qty</div>
                    <div className="col-span-2 text-right">Price</div>
                    <div className="col-span-2 text-right">Total</div>
                  </div>
                  {selectedOrder.items.map((item: any, index: number) => (
                    <div key={item.id} className={`grid grid-cols-12 gap-2 p-3 text-sm ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}>
                      <div className="col-span-6 font-medium text-gray-900">{item.name}</div>
                      <div className="col-span-2 text-center text-gray-700">x{item.quantity}</div>
                      <div className="col-span-2 text-right text-gray-700">₹{item.price}</div>
                      <div className="col-span-2 text-right font-semibold text-gray-900">₹{item.total}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Discount */}
              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                <h3 className="font-semibold mb-3 text-yellow-800 flex items-center gap-2">
                  <Percent className="h-4 w-4" />
                  Discount
                </h3>
                <div className="flex gap-3">
                  <select
                    value={discountType}
                    onChange={(e) => setDiscountType(e.target.value as "percentage" | "amount")}
                    className="px-4 py-2 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 bg-white min-w-[140px]"
                  >
                    <option value="percentage">Percentage (%)</option>
                    <option value="amount">Amount (₹)</option>
                  </select>
                  <Input
                    type="number"
                    placeholder={discountType === "percentage" ? "Enter %" : "Enter amount"}
                    value={discountValue}
                    onChange={(e) => setDiscountValue(e.target.value)}
                    className="flex-1 border-yellow-300 focus:ring-yellow-500"
                  />
                </div>
                {discountValue && (
                  <p className="text-sm text-yellow-700 mt-2">
                    Discount: {discountType === "percentage" ? `${discountValue}%` : `₹${discountValue}`}
                    = ₹{currentBill.discountAmount.toFixed(2)}
                  </p>
                )}
              </div>

              {/* GST Toggle */}
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Calculator className="h-5 w-5 text-yellow-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">GST (CGST + SGST)</h3>
                      <p className="text-sm text-gray-600">Include {gstSettings.totalGSTRate}% GST in this bill</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${includeGST ? 'text-green-600' : 'text-gray-500'}`}>
                      {includeGST ? 'With GST' : 'Without GST'}
                    </span>
                    <Switch
                      checked={includeGST}
                      onCheckedChange={setIncludeGST}
                      className="data-[state=checked]:bg-green-600"
                    />
                  </div>
                </div>
                {!includeGST && (
                  <div className="mt-2 p-2 bg-orange-100 rounded text-sm text-orange-800">
                    <strong>Note:</strong> GST will not be included in this bill. This may be required for certain business transactions.
                  </div>
                )}
              </div>

              {/* Bill Summary */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3">Bill Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>₹{currentBill.subtotal.toFixed(2)}</span>
                  </div>
                  {currentBill.discountAmount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount</span>
                      <span>-₹{currentBill.discountAmount.toFixed(2)}</span>
                    </div>
                  )}
                  {includeGST && (
                    <>
                      <div className="flex justify-between">
                        <span>CGST ({gstSettings.cgstRate}%)</span>
                        <span>₹{currentBill.cgst.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>SGST ({gstSettings.sgstRate}%)</span>
                        <span>₹{currentBill.sgst.toFixed(2)}</span>
                      </div>
                    </>
                  )}
                  {!includeGST && (
                    <div className="flex justify-between text-orange-600">
                      <span>GST (Excluded)</span>
                      <span>₹0.00</span>
                    </div>
                  )}

                  {currentBill.deliveryCharges > 0 && (
                    <div className="flex justify-between">
                      <span>Delivery Charges</span>
                      <span>₹{currentBill.deliveryCharges.toFixed(2)}</span>
                    </div>
                  )}
                  {currentBill.packagingCharges > 0 && (
                    <div className="flex justify-between">
                      <span>Packaging Charges</span>
                      <span>₹{currentBill.packagingCharges.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="border-t pt-2 flex justify-between font-bold text-lg">
                    <span>Grand Total</span>
                    <span>₹{currentBill.grandTotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <h3 className="font-semibold mb-2">Payment Method</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  <Button
                    variant={paymentMethod === "cash" ? "default" : "outline"}
                    onClick={() => setPaymentMethod("cash")}
                    className="flex items-center gap-2"
                  >
                    <Banknote className="h-4 w-4" />
                    Cash
                  </Button>
                  <Button
                    variant={paymentMethod === "card" ? "default" : "outline"}
                    onClick={() => setPaymentMethod("card")}
                    className="flex items-center gap-2"
                  >
                    <CreditCard className="h-4 w-4" />
                    Card
                  </Button>
                  <Button
                    variant={paymentMethod === "upi" ? "default" : "outline"}
                    onClick={() => setPaymentMethod("upi")}
                    className="flex items-center gap-2"
                  >
                    <Smartphone className="h-4 w-4" />
                    UPI
                  </Button>
                  <Button
                    variant={paymentMethod === "online" ? "default" : "outline"}
                    onClick={() => setPaymentMethod("online")}
                    className="flex items-center gap-2"
                  >
                    <CreditCard className="h-4 w-4" />
                    Online
                  </Button>
                </div>
              </div>

              {/* Customer Notes */}
              <div>
                <Label htmlFor="notes">Customer Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Any special notes for the customer..."
                  value={customerNotes}
                  onChange={(e) => setCustomerNotes(e.target.value)}
                  className="mt-1"
                  rows={3}
                />
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => setIsBillDialogOpen(false)}
                  className="h-12 px-6 border-gray-300 hover:bg-gray-100"
                >
                  Cancel
                </Button>
                <Button
                  variant="outline"
                  onClick={handleDownloadBill}
                  className="h-12 px-6 flex items-center gap-2 border-blue-300 text-blue-700 hover:bg-blue-50"
                >
                  <Download className="h-4 w-4" />
                  Download PDF
                </Button>
                <Button
                  onClick={handlePrintBill}
                  className="h-12 px-6 flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
                >
                  <Printer className="h-4 w-4" />
                  Print & Complete
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Customer Selection Dialog */}
      <CustomerSelectionDialog
        isOpen={showCustomerDialog}
        onClose={() => setShowCustomerDialog(false)}
        onCustomerSelect={handleCustomerSelection}
        title="Select Customer for Bill"
        description="Choose a customer for this bill or skip to print without customer details"
      />

      {/* Payment Method Selection Dialog */}
      <PaymentMethodDialog
        open={showPaymentMethodDialog}
        onClose={() => setShowPaymentMethodDialog(false)}
        onSelect={handlePaymentMethodSelection}
        title="Select Payment Method"
      />
    </div>
  );
};

export default BillGeneration;
