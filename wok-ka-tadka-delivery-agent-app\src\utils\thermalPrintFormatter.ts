// Thermal Print Formatter for 58mm thermal printers
// Converts bill and KOT data to thermal printer format

import { KOT, KOTItem } from './kotStorage';
import { Customer } from './customerStorage';
import { gstSettingsManager } from './gstSettings';
import { restaurantSettingsManager } from './restaurantSettings';

export interface ThermalPrintOptions {
  paperWidth: number; // in characters (usually 32 for 58mm)
  fontSize: 'small' | 'normal' | 'large';
  bold: boolean;
  center: boolean;
}

export class ThermalPrintFormatter {
  private readonly PAPER_WIDTH = 24; // 58mm paper = ~24 characters (very safe for thermal printers)
  private readonly SEPARATOR = '-'.repeat(this.PAPER_WIDTH);

  // Center text within paper width
  private centerText(text: string): string {
    const padding = Math.max(0, Math.floor((this.PAPER_WIDTH - text.length) / 2));
    return ' '.repeat(padding) + text;
  }

  // Pad text to fit line with alignment
  private padLine(left: string, right: string): string {
    const totalLength = left.length + right.length;
    if (totalLength >= this.PAPER_WIDTH) {
      // If too long, truncate left side but keep right side intact
      const maxLeftLength = this.PAPER_WIDTH - right.length - 1;
      return left.substring(0, maxLeftLength) + ' ' + right;
    }
    const spaces = this.PAPER_WIDTH - totalLength;
    return left + ' '.repeat(spaces) + right;
  }

  // Format currency
  private formatCurrency(amount: number): string {
    return `₹${amount.toFixed(2)}`;
  }

  // Format date and time
  private formatDateTime(date: Date = new Date()): { date: string; time: string } {
    return {
      date: date.toLocaleDateString('en-IN'),
      time: date.toLocaleTimeString('en-IN', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      })
    };
  }

  // Format KOT for thermal printing
  formatKOT(kot: KOT): string {
    const { date, time } = this.formatDateTime(new Date(kot.createdAt));
    const restaurantInfo = restaurantSettingsManager.getFormattedHeader();

    let output = '';

    // Header
    output += this.centerText(restaurantInfo.name) + '\n';
    output += this.centerText(restaurantInfo.address) + '\n';
    output += this.centerText(`Ph: ${restaurantInfo.phone}`) + '\n';
    output += this.SEPARATOR + '\n';
    output += this.centerText('KOT') + '\n';
    output += this.centerText('Kitchen Order') + '\n';
    output += this.SEPARATOR + '\n';
    
    // KOT Details
    output += `KOT No: ${kot.kotNumber}\n`;
    output += `Table: ${kot.tableId}\n`;
    output += `Date: ${date}\n`;
    output += `Time: ${time}\n`;
    output += this.SEPARATOR + '\n';
    
    // Items
    output += 'ITEMS:\n';
    output += this.SEPARATOR + '\n';
    
    kot.items.forEach((item: KOTItem) => {
      // Item name (truncate if too long)
      const itemName = item.name.length > this.PAPER_WIDTH ?
        item.name.substring(0, this.PAPER_WIDTH - 3) + '...' :
        item.name;
      output += `${itemName}\n`;

      // Quantity
      output += `Qty: ${item.quantity}\n`;

      // Price
      output += `Price: ₹${item.price.toFixed(2)}\n`;

      // Special instructions if any
      if (item.specialInstructions) {
        output += `Note: ${item.specialInstructions}\n`;
      }
      output += '\n';
    });
    
    // Special instructions for the whole order
    if (kot.specialInstructions) {
      output += this.SEPARATOR + '\n';
      output += 'SPECIAL INSTRUCTIONS:\n';
      output += kot.specialInstructions + '\n';
    }
    
    output += this.SEPARATOR + '\n';
    output += this.centerText('KITCHEN COPY') + '\n';
    output += '\n\n\n'; // Extra lines for cutting
    
    return output;
  }

  // Format Bill for thermal printing
  formatBill(
    kot: KOT, 
    customer: Customer | null, 
    includeGST: boolean = true,
    discount: number = 0,
    discountType: 'percentage' | 'fixed' = 'percentage',
    paymentMethod: string = 'cash'
  ): string {
    const { date, time } = this.formatDateTime();

    // Get GST settings and restaurant information
    const gstSettings = gstSettingsManager.getGSTSettings();
    const restaurantInfo = restaurantSettingsManager.getFormattedHeader();

    let output = '';

    // Header
    output += this.centerText(restaurantInfo.name) + '\n';
    output += this.centerText('BILL') + '\n';
    output += this.centerText(restaurantInfo.address) + '\n';
    output += this.centerText(`Ph: ${restaurantInfo.phone}`) + '\n';
    output += this.centerText(`GST: ${restaurantSettingsManager.getGSTNumber()}`) + '\n';
    output += this.SEPARATOR + '\n';
    
    // Bill Details
    output += `Bill No: ${kot.kotNumber}\n`;
    output += `Table: ${kot.tableId}\n`;
    output += `Date: ${date}\n`;
    output += `Time: ${time}\n`;
    
    // Customer Details
    if (customer) {
      output += this.SEPARATOR + '\n';
      output += 'CUSTOMER:\n';
      // Truncate long names to fit
      const customerName = customer.name.length > 18 ?
        customer.name.substring(0, 15) + '...' :
        customer.name;
      output += `${customerName}\n`;
      if (customer.phone) {
        output += `${customer.phone}\n`;
      }
      if (customer.isRegular) {
        output += 'Regular Customer ⭐\n';
      }
    }
    
    output += this.SEPARATOR + '\n';
    
    // Items
    output += 'ITEMS:\n';
    output += this.SEPARATOR + '\n';
    
    let subtotal = 0;
    
    kot.items.forEach((item: KOTItem) => {
      const itemTotal = item.price * item.quantity;
      subtotal += itemTotal;

      // Item name (truncate if too long)
      const itemName = item.name.length > this.PAPER_WIDTH ?
        item.name.substring(0, this.PAPER_WIDTH - 3) + '...' :
        item.name;
      output += `${itemName}\n`;

      // Quantity
      output += `Qty: ${item.quantity}\n`;

      // Unit price
      output += `Rate: ₹${item.price.toFixed(2)}\n`;

      // Item total
      output += `Amount: ₹${itemTotal.toFixed(2)}\n`;
      output += '\n'; // Add spacing between items
    });
    
    output += this.SEPARATOR + '\n';
    
    // Calculations - each amount on its own line
    output += this.SEPARATOR + '\n';
    output += 'Subtotal:\n';
    output += `₹${subtotal.toFixed(2)}\n`;
    output += '\n';

    // Discount
    if (discount > 0) {
      const discountAmount = discountType === 'percentage'
        ? (subtotal * discount) / 100
        : discount;
      const discountLabel = discountType === 'percentage' ? `Discount ${discount}%:` : 'Discount:';
      output += discountLabel + '\n';
      output += `-₹${discountAmount.toFixed(2)}\n`;
      output += '\n';
      subtotal -= discountAmount;
    }

    // GST
    let cgstAmount = 0;
    let sgstAmount = 0;
    let gstAmount = 0;
    if (includeGST) {
      cgstAmount = (subtotal * gstSettings.cgstRate) / 100;
      sgstAmount = (subtotal * gstSettings.sgstRate) / 100;
      gstAmount = cgstAmount + sgstAmount;
      output += `CGST (${gstSettings.cgstRate}%):\n`;
      output += `₹${cgstAmount.toFixed(2)}\n`;
      output += '\n';
      output += `SGST (${gstSettings.sgstRate}%):\n`;
      output += `₹${sgstAmount.toFixed(2)}\n`;
      output += '\n';
    }

    const grandTotal = subtotal + gstAmount;

    output += this.SEPARATOR + '\n';
    output += 'GRAND TOTAL:\n';
    output += `₹${grandTotal.toFixed(2)}\n`;
    output += this.SEPARATOR + '\n';
    
    // Payment Method
    output += `Payment: ${paymentMethod.toUpperCase()}\n`;
    
    // Footer
    output += this.SEPARATOR + '\n';
    output += this.centerText('Thank you for dining!') + '\n';
    output += this.centerText('Visit us again soon!') + '\n';
    output += this.centerText('www.wokkatadka.com') + '\n';
    
    output += '\n\n\n'; // Extra lines for cutting
    
    return output;
  }

  // Format simple receipt
  formatReceipt(
    orderNumber: string,
    items: Array<{ name: string; quantity: number; price: number }>,
    total: number
  ): string {
    const { date, time } = this.formatDateTime();
    
    let output = '';
    
    // Header
    output += this.centerText('WOK KA TADKA') + '\n';
    output += this.centerText('ORDER RECEIPT') + '\n';
    output += this.SEPARATOR + '\n';
    
    // Order Details
    output += `Order: ${orderNumber}\n`;
    output += `Date: ${date}\n`;
    output += `Time: ${time}\n`;
    output += this.SEPARATOR + '\n';
    
    // Items
    items.forEach(item => {
      const itemTotal = item.price * item.quantity;

      // Item name (truncate if too long)
      const itemName = item.name.length > this.PAPER_WIDTH ?
        item.name.substring(0, this.PAPER_WIDTH - 3) + '...' :
        item.name;
      output += `${itemName}\n`;

      // Quantity
      output += `Qty: ${item.quantity}\n`;

      // Unit price
      output += `Rate: ₹${item.price.toFixed(2)}\n`;

      // Item total
      output += `Amount: ₹${itemTotal.toFixed(2)}\n`;
      output += '\n'; // Add spacing between items
    });

    output += this.SEPARATOR + '\n';
    output += 'TOTAL:\n';
    output += `₹${total.toFixed(2)}\n`;
    output += this.SEPARATOR + '\n';
    
    output += this.centerText('Thank you!') + '\n';
    output += '\n\n\n';
    
    return output;
  }

  // Format test print
  formatTestPrint(): string {
    const { date, time } = this.formatDateTime();
    
    let output = '';
    
    output += this.centerText('WOK KA TADKA') + '\n';
    output += this.centerText('PRINTER TEST') + '\n';
    output += this.SEPARATOR + '\n';
    
    output += `Date: ${date}\n`;
    output += `Time: ${time}\n`;
    output += this.SEPARATOR + '\n';
    
    output += 'Printer Status: OK\n';
    output += 'Connection: Active\n';
    output += 'Paper: Ready\n';
    
    output += this.SEPARATOR + '\n';
    output += this.centerText('Test Successful!') + '\n';
    output += '\n\n\n';
    
    return output;
  }

  // Format error message
  formatError(error: string): string {
    let output = '';
    
    output += this.centerText('PRINT ERROR') + '\n';
    output += this.SEPARATOR + '\n';
    output += error + '\n';
    output += this.SEPARATOR + '\n';
    output += this.centerText('Please try again') + '\n';
    output += '\n\n\n';
    
    return output;
  }
}

// Export singleton instance
export const thermalFormatter = new ThermalPrintFormatter();
