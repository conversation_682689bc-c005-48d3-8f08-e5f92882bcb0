
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { QrCode, X } from "lucide-react";

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
}

const QRCodeModal = ({ isOpen, onClose, amount }: QRCodeModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5 text-primary" />
            Payment QR Code
          </DialogTitle>
        </DialogHeader>
        
        <div className="text-center space-y-4">
          <div className="bg-white p-6 rounded-lg border-2 border-dashed border-gray-300 mx-auto w-64 h-64 flex items-center justify-center">
            <div className="text-center">
              <QrCode className="h-24 w-24 text-gray-400 mx-auto mb-4" />
              <p className="text-sm text-gray-500 mb-2">QR Code for Payment</p>
              <p className="text-lg font-bold text-primary">₹{amount}</p>
              <p className="text-xs text-gray-400 mt-2">
                Show this to customer for scanning
              </p>
            </div>
          </div>
          
          <div className="bg-amber-50 p-3 rounded-lg border border-amber-200">
            <p className="text-sm text-amber-800">
              <strong>Instructions:</strong> Ask customer to scan this QR code with their payment app
            </p>
          </div>
          
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full"
          >
            <X className="h-4 w-4 mr-2" />
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QRCodeModal;
