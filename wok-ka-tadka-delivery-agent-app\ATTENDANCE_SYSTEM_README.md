# Waiter Attendance System

## Overview
The attendance system allows waiters to mark their daily attendance with check-in and check-out functionality. It provides a comprehensive tracking system for work hours and attendance history.

## Features

### 1. **Daily Attendance Marking**
- **Check In**: Waiters can mark their arrival for the day
- **Check Out**: Waiters can mark their departure and automatically calculate total hours worked
- **Single Check-in/Check-out per day**: Prevents duplicate entries for the same day
- **Real-time validation**: System validates if staff can check in or check out

### 2. **Attendance Dashboard Integration**
- **Dashboard Widget**: Attendance section integrated into the waiter dashboard
- **Current Status Display**: Shows today's attendance status (checked-in, checked-out, or not marked)
- **Quick Actions**: Easy check-in/check-out buttons directly from dashboard
- **Time Display**: Shows check-in and check-out times with total hours worked

### 3. **Attendance History Page**
- **Complete History**: View all past attendance records
- **Statistics**: Shows attendance statistics for different time periods (7, 15, 30, 60 days)
- **Detailed Records**: Each record shows date, check-in/out times, total hours, and notes
- **Period Selection**: Filter attendance data by different time ranges

### 4. **Statistics & Analytics**
- **Present Days**: Count of days attended
- **Absent Days**: Count of days not attended
- **Total Hours**: Sum of all hours worked
- **Average Hours**: Average hours worked per day

## Technical Implementation

### Files Created/Modified

#### 1. **src/utils/attendanceStorage.ts**
- Core attendance management utility
- Handles all localStorage operations for attendance data
- Provides functions for check-in, check-out, and data retrieval
- Includes validation and calculation functions

#### 2. **src/pages/delivery/Attendance.tsx**
- Dedicated attendance history page
- Shows comprehensive attendance records and statistics
- Mobile-responsive design with period selection
- Integrated with the existing UI design system

#### 3. **src/pages/delivery/Dashboard.tsx** (Modified)
- Added attendance section to waiter dashboard
- Integrated check-in/check-out functionality
- Shows current attendance status
- Added navigation to attendance history page

#### 4. **src/App.tsx** (Modified)
- Added route for attendance page: `/delivery/attendance`

### Data Structure

```typescript
interface AttendanceRecord {
  id: string;                    // Unique identifier (staffId-date)
  staffId: string;              // Staff member ID
  staffName: string;            // Staff member name
  staffPhone: string;           // Staff member phone
  date: string;                 // Date in YYYY-MM-DD format
  checkInTime?: string;         // Check-in time (ISO string)
  checkOutTime?: string;        // Check-out time (ISO string)
  status: 'checked-in' | 'checked-out' | 'absent';
  totalHours?: number;          // Total hours worked (calculated)
  notes?: string;               // Optional notes
  location?: string;            // Optional location info
}
```

### Key Functions

#### Core Functions
- `markCheckIn(staffId, staffName, staffPhone, notes?)`: Mark staff check-in
- `markCheckOut(staffId, notes?)`: Mark staff check-out
- `getTodayAttendance(staffId)`: Get today's attendance record
- `canCheckIn(staffId)`: Check if staff can check in
- `canCheckOut(staffId)`: Check if staff can check out

#### Data Retrieval
- `getStaffAttendanceRecords(staffId, limit?)`: Get attendance history
- `getStaffAttendanceStats(staffId, days)`: Get attendance statistics
- `getAttendanceByDateRange(startDate, endDate)`: Get records by date range

#### Utility Functions
- `formatTime(isoString)`: Format time for display
- `formatDate(dateString)`: Format date for display
- `calculateHours(checkIn, checkOut)`: Calculate hours between times

## Usage

### For Waiters

1. **Daily Check-in**:
   - Open waiter dashboard
   - Click "Check In" button in the attendance section
   - System records current time and marks as checked-in

2. **Daily Check-out**:
   - Click "Check Out" button (only available after check-in)
   - System calculates total hours worked
   - Shows confirmation with hours worked

3. **View Attendance History**:
   - Click "View History" button in attendance section
   - Or navigate to `/delivery/attendance`
   - Select time period (7, 15, 30, or 60 days)
   - View detailed records and statistics

### For Administrators

The attendance data is stored in localStorage and can be accessed for:
- Staff performance monitoring
- Payroll calculations
- Attendance reports
- Work hour tracking

## Data Storage

- **Storage Location**: Browser localStorage
- **Storage Key**: `wok_ka_tadka_attendance`
- **Data Format**: JSON array of AttendanceRecord objects
- **Persistence**: Data persists across browser sessions

## Mobile Responsiveness

- Fully responsive design for all screen sizes
- Touch-friendly buttons and interface
- Optimized for mobile devices used by waiters
- Proper spacing to prevent UI overlap with system navigation

## Integration with Existing System

- **Staff Authentication**: Uses existing staff PIN login system
- **Staff Data**: Integrates with staff information from login
- **UI Design**: Follows existing design patterns and components
- **Navigation**: Integrated with existing routing system

## Testing

A test utility is provided in `src/utils/testAttendanceSystem.ts`:

```typescript
import { testAttendanceSystem, clearTestAttendanceData } from '@/utils/testAttendanceSystem';

// Run tests
testAttendanceSystem();

// Clear test data
clearTestAttendanceData();
```

## Future Enhancements

Potential improvements that could be added:
- GPS location tracking for check-in/out
- Photo capture for attendance verification
- Break time tracking
- Overtime calculations
- Export attendance reports
- Admin dashboard for attendance management
- Push notifications for attendance reminders
- Integration with payroll systems

## Security Considerations

- Data stored locally in browser
- No sensitive information exposed
- Staff ID validation through existing PIN system
- Attendance records tied to authenticated staff members

## Browser Compatibility

- Works in all modern browsers
- Requires localStorage support
- Mobile browser compatible
- Progressive Web App ready
