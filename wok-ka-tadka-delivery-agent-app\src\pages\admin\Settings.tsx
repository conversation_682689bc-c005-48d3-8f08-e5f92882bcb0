import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { gstSettingsManager, GSTSettings } from "@/utils/gstSettings";
import { restaurantSettingsManager, RestaurantSettings } from "@/utils/restaurantSettings";
import {
  getWorkingHoursSettings,
  updateWorkingHours,
  resetWorkingHoursToDefault,
  validateAndUpdateWorkingHours,
  getWorkingHoursSummary,
  type WorkingHours,
  type WorkingHoursSettings
} from "@/utils/workingHoursStorage";
import {
  Arrow<PERSON>ef<PERSON>,
  Settings as SettingsIcon,
  Save,
  RefreshCw,
  Bell,
  Shield,
  Palette,
  Globe,
  Clock,
  Printer,
  Calculator,
  Percent,
  Users,
  Timer
} from "lucide-react";
import { Logo } from "@/components/ui/logo";
import PrinterSetup from "@/components/PrinterSetup";
import { setGlobalPrinterService, ThermalPrinterService } from "@/services/thermalPrinter";

const Settings = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Handle printer configuration
  const handlePrinterConfigured = (printer: ThermalPrinterService) => {
    setGlobalPrinterService(printer);
    toast({
      title: "Printer Configured",
      description: "Thermal printer has been successfully configured and connected.",
    });
  };

  // Restaurant Settings - Load from restaurantSettingsManager
  const [restaurantSettings, setRestaurantSettings] = useState<RestaurantSettings>(
    restaurantSettingsManager.getRestaurantSettings()
  );

  // GST Settings
  const [gstSettings, setGSTSettings] = useState<GSTSettings>(
    gstSettingsManager.getGSTSettings()
  );

  // Working Hours Settings
  const [workingHoursSettings, setWorkingHoursSettings] = useState<WorkingHoursSettings>(
    getWorkingHoursSettings()
  );

  // Notification Settings
  const [notifications, setNotifications] = useState({
    orderAlerts: true,
    lowStockAlerts: true,
    dailyReports: true,
    emailNotifications: false,
    smsNotifications: true
  });

  // System Settings - Load from localStorage or use defaults
  const [systemSettings, setSystemSettings] = useState(() => {
    const stored = localStorage.getItem('systemSettings');
    return stored ? JSON.parse(stored) : {
      autoBackup: true,
      printKOTAutomatically: true,
      showItemImages: true,
      enableTableReservations: false,
      taxRate: 18
    };
  });

  // Listen for GST settings changes
  useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent<GSTSettings>) => {
      setGSTSettings(event.detail);
    };

    const handleWorkingHoursChange = (event: CustomEvent<WorkingHoursSettings>) => {
      setWorkingHoursSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    window.addEventListener('workingHoursChanged', handleWorkingHoursChange as EventListener);

    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
      window.removeEventListener('workingHoursChanged', handleWorkingHoursChange as EventListener);
    };
  }, []);

  const handleSaveSettings = () => {
    // Save restaurant settings using the manager
    restaurantSettingsManager.saveRestaurantSettings(restaurantSettings);

    // Save other settings to localStorage
    localStorage.setItem('notificationSettings', JSON.stringify(notifications));
    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));

    // Trigger a custom event to notify all components about system settings change
    window.dispatchEvent(new CustomEvent('systemSettingsChanged', {
      detail: systemSettings
    }));

    // GST settings are automatically saved when changed via gstSettingsManager
    // Working hours settings are automatically saved when changed via updateWorkingHours

    toast({
      title: "Settings Saved!",
      description: "All settings including working hours and GST configuration have been saved successfully.",
    });
  };

  // GST Settings Handlers
  const handleGSTRateChange = (field: 'cgstRate' | 'sgstRate', value: string) => {
    const numValue = parseFloat(value) || 0;
    if (numValue < 0 || numValue > 50) return;

    try {
      if (field === 'cgstRate') {
        gstSettingsManager.updateGSTRates(numValue, gstSettings.sgstRate);
      } else {
        gstSettingsManager.updateGSTRates(gstSettings.cgstRate, numValue);
      }

      toast({
        title: "GST Rate Updated!",
        description: `${field === 'cgstRate' ? 'CGST' : 'SGST'} rate updated to ${numValue}%`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update GST rate",
        variant: "destructive",
      });
    }
  };

  const handleTotalGSTRateChange = (value: string) => {
    const numValue = parseFloat(value) || 0;
    if (numValue < 0 || numValue > 100) return;

    try {
      gstSettingsManager.setTotalGSTRate(numValue);
      toast({
        title: "GST Rate Updated!",
        description: `Total GST rate updated to ${numValue}% (CGST: ${numValue/2}%, SGST: ${numValue/2}%)`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update GST rate",
        variant: "destructive",
      });
    }
  };

  const handleWorkingHoursChange = (field: keyof WorkingHours, value: any) => {
    const updatedWorkingHours = {
      ...workingHoursSettings.workingHours,
      [field]: value
    };

    // Auto-calculate total hours when start/end time changes
    if (field === 'startTime' || field === 'endTime') {
      const [startHour, startMinute] = updatedWorkingHours.startTime.split(':').map(Number);
      const [endHour, endMinute] = updatedWorkingHours.endTime.split(':').map(Number);

      const startTimeInMinutes = startHour * 60 + startMinute;
      const endTimeInMinutes = endHour * 60 + endMinute;

      if (endTimeInMinutes > startTimeInMinutes) {
        const totalMinutes = endTimeInMinutes - startTimeInMinutes - updatedWorkingHours.breakDuration;
        updatedWorkingHours.totalHours = Math.round((totalMinutes / 60) * 10) / 10; // Round to 1 decimal
      }
    }

    // Use validation when updating working hours
    const result = validateAndUpdateWorkingHours(updatedWorkingHours, 'admin');

    if (!result.success) {
      // Show validation errors
      const errorMessage = result.validation.errors.join(', ');
      toast({
        title: "Invalid Working Hours Configuration",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    // Show warnings if any
    if (result.validation.warnings.length > 0) {
      const warningMessage = result.validation.warnings.join(', ');
      toast({
        title: "Working Hours Updated with Warnings",
        description: warningMessage,
        variant: "default",
      });
    }
  };

  const handleWorkingDaysChange = (day: string, checked: boolean) => {
    const currentDays = workingHoursSettings.workingHours.workingDays;
    const updatedDays = checked
      ? [...currentDays, day]
      : currentDays.filter(d => d !== day);

    handleWorkingHoursChange('workingDays', updatedDays);
  };

  const handleResetSettings = () => {
    // Reset restaurant settings using the manager
    restaurantSettingsManager.resetToDefaults();
    setRestaurantSettings(restaurantSettingsManager.getRestaurantSettings());

    // Reset GST settings
    gstSettingsManager.resetToDefaults();

    // Reset working hours settings
    resetWorkingHoursToDefault();

    toast({
      title: "Settings Reset!",
      description: "All settings including working hours and GST configuration have been reset to default values.",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/admin/dashboard")}
                className="flex items-center gap-1 sm:gap-2 shrink-0"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <Logo className="h-6 sm:h-8 shrink-0" />
              <div className="min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Settings</h1>
                <p className="text-xs sm:text-sm text-gray-600 truncate">Configure restaurant settings</p>
              </div>
            </div>
            <div className="flex gap-1 sm:gap-2 shrink-0">
              <Button variant="outline" onClick={handleResetSettings} className="text-xs sm:text-sm">
                <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Reset</span>
              </Button>
              <Button onClick={handleSaveSettings} className="text-xs sm:text-sm">
                <Save className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Save Settings</span>
                <span className="sm:hidden">Save</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 space-y-4 sm:space-y-6">
        {/* Restaurant Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Globe className="h-4 w-4 sm:h-5 sm:w-5" />
              Restaurant Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="restaurantName" className="text-sm sm:text-base">Restaurant Name</Label>
                <Input
                  id="restaurantName"
                  value={restaurantSettings.name}
                  onChange={(e) => setRestaurantSettings({...restaurantSettings, name: e.target.value})}
                  className="mt-1 text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="phone" className="text-sm sm:text-base">Phone Number</Label>
                <Input
                  id="phone"
                  value={restaurantSettings.phone}
                  onChange={(e) => setRestaurantSettings({...restaurantSettings, phone: e.target.value})}
                  className="mt-1 text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="email" className="text-sm sm:text-base">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={restaurantSettings.email}
                  onChange={(e) => setRestaurantSettings({...restaurantSettings, email: e.target.value})}
                  className="mt-1 text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="gst" className="text-sm sm:text-base">GST Number</Label>
                <Input
                  id="gst"
                  value={restaurantSettings.gst}
                  onChange={(e) => setRestaurantSettings({...restaurantSettings, gst: e.target.value})}
                  className="mt-1 text-sm sm:text-base"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="address" className="text-sm sm:text-base">Address</Label>
              <Textarea
                id="address"
                value={restaurantSettings.address}
                onChange={(e) => setRestaurantSettings({...restaurantSettings, address: e.target.value})}
                className="mt-1 text-sm sm:text-base"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="orderAlerts">Order Alerts</Label>
                <p className="text-sm text-gray-600">Get notified when new orders arrive</p>
              </div>
              <Switch
                id="orderAlerts"
                checked={notifications.orderAlerts}
                onCheckedChange={(checked) => setNotifications({...notifications, orderAlerts: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="lowStockAlerts">Low Stock Alerts</Label>
                <p className="text-sm text-gray-600">Get notified when inventory is low</p>
              </div>
              <Switch
                id="lowStockAlerts"
                checked={notifications.lowStockAlerts}
                onCheckedChange={(checked) => setNotifications({...notifications, lowStockAlerts: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="dailyReports">Daily Reports</Label>
                <p className="text-sm text-gray-600">Receive daily sales and performance reports</p>
              </div>
              <Switch
                id="dailyReports"
                checked={notifications.dailyReports}
                onCheckedChange={(checked) => setNotifications({...notifications, dailyReports: checked})}
              />
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5" />
              System Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="taxRate">Tax Rate (%)</Label>
                <Input
                  id="taxRate"
                  type="number"
                  value={systemSettings.taxRate}
                  onChange={(e) => setSystemSettings({...systemSettings, taxRate: Number(e.target.value)})}
                  className="mt-1"
                />
              </div>

            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="autoBackup">Auto Backup</Label>
                <p className="text-sm text-gray-600">Automatically backup data daily</p>
              </div>
              <Switch
                id="autoBackup"
                checked={systemSettings.autoBackup}
                onCheckedChange={(checked) => setSystemSettings({...systemSettings, autoBackup: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="printKOT">Auto Print KOT</Label>
                <p className="text-sm text-gray-600">Automatically print KOT when generated</p>
              </div>
              <Switch
                id="printKOT"
                checked={systemSettings.printKOTAutomatically}
                onCheckedChange={(checked) => setSystemSettings({...systemSettings, printKOTAutomatically: checked})}
              />
            </div>
          </CardContent>
        </Card>

        {/* Printer Settings */}
        <Card className="border-green-200 bg-green-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Printer className="h-5 w-5" />
              Thermal Printer Configuration
            </CardTitle>
            <p className="text-sm text-green-700">
              Configure your Everycom EC58 thermal printer for KOT and bill printing.
            </p>
          </CardHeader>
          <CardContent>
            <PrinterSetup onPrinterConfigured={handlePrinterConfigured} />
          </CardContent>
        </Card>

        {/* Working Hours Settings */}
        <Card className="border-blue-200 bg-blue-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Clock className="h-5 w-5" />
              Staff Working Hours Configuration
            </CardTitle>
            <p className="text-sm text-blue-700">
              Set working hours for all staff members. This affects attendance tracking and early checkout approvals.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Working Time Settings */}
            <div className="bg-white p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-4">
                <Timer className="h-4 w-4 text-blue-600" />
                <Label className="font-semibold">Daily Working Hours</Label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <Label htmlFor="startTime">Start Time</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={workingHoursSettings.workingHours.startTime}
                    onChange={(e) => handleWorkingHoursChange('startTime', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="endTime">End Time</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={workingHoursSettings.workingHours.endTime}
                    onChange={(e) => handleWorkingHoursChange('endTime', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="totalHours">Total Hours</Label>
                  <Input
                    id="totalHours"
                    type="number"
                    min="1"
                    max="24"
                    step="0.5"
                    value={workingHoursSettings.workingHours.totalHours}
                    onChange={(e) => handleWorkingHoursChange('totalHours', parseFloat(e.target.value))}
                    className="mt-1"
                    readOnly
                  />
                  <p className="text-xs text-gray-500 mt-1">Auto-calculated</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="breakDuration">Break Duration (minutes)</Label>
                  <Input
                    id="breakDuration"
                    type="number"
                    min="0"
                    max="240"
                    value={workingHoursSettings.workingHours.breakDuration}
                    onChange={(e) => handleWorkingHoursChange('breakDuration', parseInt(e.target.value))}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="earlyCheckoutThreshold">Early Checkout Threshold (minutes)</Label>
                  <Input
                    id="earlyCheckoutThreshold"
                    type="number"
                    min="0"
                    max="120"
                    value={workingHoursSettings.workingHours.earlyCheckoutThreshold}
                    onChange={(e) => handleWorkingHoursChange('earlyCheckoutThreshold', parseInt(e.target.value))}
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">Minutes before end time that requires approval</p>
                </div>
              </div>
            </div>

            {/* Working Days Settings */}
            <div className="bg-white p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-4">
                <Users className="h-4 w-4 text-blue-600" />
                <Label className="font-semibold">Working Days</Label>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                  <div key={day} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={day}
                      checked={workingHoursSettings.workingHours.workingDays.includes(day)}
                      onChange={(e) => handleWorkingDaysChange(day, e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor={day} className="text-sm capitalize cursor-pointer">
                      {day}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Current Settings Display */}
            <div className="bg-blue-100 p-4 rounded-lg border border-blue-300">
              <h4 className="font-medium text-blue-900 mb-2">Current Configuration</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Working Hours:</span>
                  <span className="font-medium ml-2">
                    {new Date(`2000-01-01T${workingHoursSettings.workingHours.startTime}`).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: true})} -
                    {new Date(`2000-01-01T${workingHoursSettings.workingHours.endTime}`).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: true})}
                  </span>
                </div>
                <div>
                  <span className="text-blue-700">Total Hours:</span>
                  <span className="font-medium ml-2">{workingHoursSettings.workingHours.totalHours}h per day</span>
                </div>
                <div>
                  <span className="text-blue-700">Working Days:</span>
                  <span className="font-medium ml-2 capitalize">
                    {workingHoursSettings.workingHours.workingDays.join(', ')}
                  </span>
                </div>
                <div>
                  <span className="text-blue-700">Break Duration:</span>
                  <span className="font-medium ml-2">{workingHoursSettings.workingHours.breakDuration} minutes</span>
                </div>
              </div>
              <div className="mt-2 text-xs text-blue-600">
                Last updated: {new Date(workingHoursSettings.lastUpdated).toLocaleString()}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* GST Settings */}
        <Card className="border-yellow-200 bg-yellow-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <Calculator className="h-5 w-5" />
              GST Configuration
            </CardTitle>
            <p className="text-sm text-yellow-700">
              Configure GST rates that will be applied across all billing components
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Quick Total GST Rate */}
            <div className="bg-white p-4 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2 mb-3">
                <Percent className="h-4 w-4 text-yellow-600" />
                <Label className="font-semibold">Total GST Rate</Label>
              </div>
              <div className="flex items-center gap-3">
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={gstSettings.totalGSTRate}
                  onChange={(e) => handleTotalGSTRateChange(e.target.value)}
                  className="w-24"
                />
                <span className="text-sm text-gray-600">
                  % (Auto-splits into CGST: {gstSettings.cgstRate}% + SGST: {gstSettings.sgstRate}%)
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                This will automatically update CGST and SGST rates equally
              </p>
            </div>

            {/* Individual GST Rates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cgstRate">CGST Rate (%)</Label>
                <Input
                  id="cgstRate"
                  type="number"
                  min="0"
                  max="50"
                  step="0.1"
                  value={gstSettings.cgstRate}
                  onChange={(e) => handleGSTRateChange('cgstRate', e.target.value)}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">Central Goods and Services Tax</p>
              </div>
              <div>
                <Label htmlFor="sgstRate">SGST Rate (%)</Label>
                <Input
                  id="sgstRate"
                  type="number"
                  min="0"
                  max="50"
                  step="0.1"
                  value={gstSettings.sgstRate}
                  onChange={(e) => handleGSTRateChange('sgstRate', e.target.value)}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">State Goods and Services Tax</p>
              </div>
            </div>

            {/* GST Status */}
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-green-800">GST Status</h4>
                  <p className="text-sm text-green-700">
                    Current GST rate: {gstSettings.totalGSTRate}%
                    (CGST: {gstSettings.cgstRate}% + SGST: {gstSettings.sgstRate}%)
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-green-600 font-medium">
                    {gstSettings.enabled ? 'Enabled by Default' : 'Disabled by Default'}
                  </div>
                  <Switch
                    checked={gstSettings.enabled}
                    onCheckedChange={(checked) => gstSettingsManager.toggleGSTEnabled(checked)}
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            {/* Info Note */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-800 mb-2">Important Notes:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Changes will automatically apply to all billing components</li>
                <li>• Users can still toggle GST on/off for individual bills</li>
                <li>• GST rates are saved locally and persist across app restarts</li>
                <li>• Last updated: {new Date(gstSettings.lastUpdated).toLocaleString()}</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
