import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { App } from '@capacitor/app';

export function useAndroidBackButton() {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    let backButtonListener: any;

    const setupBackButtonListener = async () => {
      try {
        // Add listener for hardware back button
        backButtonListener = await App.addListener('backButton', ({ canGoBack }) => {
          const currentPath = location.pathname;

          // Check if we're in a print window or popup
          if (window.opener && window.opener !== window) {
            // This is a popup/print window, close it
            window.close();
            return;
          }

          // Define navigation hierarchy
          const navigationHierarchy: { [key: string]: string } = {
            '/delivery/login': '/',
            '/delivery/dashboard': '/delivery/login',
            '/delivery/assignments': '/delivery/dashboard',
            '/delivery/current': '/delivery/dashboard',
            '/delivery/history': '/delivery/dashboard',
            '/delivery/profile': '/delivery/dashboard',
            '/delivery/tables': '/delivery/dashboard',
            '/delivery/kot-history': '/delivery/dashboard',
          };

          // Handle table-specific routes
          if (currentPath.startsWith('/delivery/table-order/')) {
            navigate('/delivery/tables');
            return;
          }
          
          if (currentPath.startsWith('/delivery/table-details/')) {
            navigate('/delivery/tables');
            return;
          }
          
          if (currentPath.startsWith('/delivery/kot/')) {
            // Check if there's a print window open
            if (window.name === '' && window.opener) {
              // This is likely a print window, close it instead of navigating
              window.close();
              return;
            }
            const tableId = currentPath.split('/').pop();
            navigate(`/delivery/table-details/${tableId}`);
            return;
          }
          
          if (currentPath.startsWith('/delivery/details/')) {
            navigate('/delivery/assignments');
            return;
          }

          // Check if we have a defined parent route
          const parentRoute = navigationHierarchy[currentPath];
          if (parentRoute) {
            navigate(parentRoute);
            return;
          }

          // If we can go back in browser history, do that
          if (canGoBack) {
            window.history.back();
            return;
          }

          // Default fallback - go to dashboard or exit app
          if (currentPath === '/delivery/dashboard' || currentPath === '/delivery/login' || currentPath === '/') {
            // Exit the app if we're at the root level
            App.exitApp();
          } else {
            navigate('/delivery/dashboard');
          }
        });
      } catch (error) {
        console.log('Back button listener setup failed (probably running in browser):', error);
      }
    };

    setupBackButtonListener();

    // Cleanup listener on unmount
    return () => {
      if (backButtonListener) {
        backButtonListener.remove();
      }
    };
  }, [navigate, location.pathname]);
}
