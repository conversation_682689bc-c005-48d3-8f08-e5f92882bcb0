// Salary Management Storage Utility for managing staff salary records and payments

export interface SalaryRecord {
  id: string;
  staffId: string;
  staffName: string;
  staffPhone: string;
  staffRole: string;
  month: string; // YYYY-MM format
  year: number;
  baseSalary: number;
  workingDays: number;
  presentDays: number;
  totalHours: number;
  overtimeHours: number;
  overtimeRate: number;
  bonuses: number;
  deductions: number;
  finalSalary: number;
  status: 'pending' | 'paid' | 'partial';
  paidAmount: number;
  paymentDate?: string;
  paymentMethod?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SalaryPayment {
  id: string;
  salaryRecordId: string;
  staffId: string;
  amount: number;
  paymentDate: string;
  paymentMethod: 'cash' | 'bank_transfer' | 'upi' | 'cheque';
  paymentType: 'salary' | 'advance'; // New field to distinguish payment types
  transactionId?: string;
  notes?: string;
  paidBy: string; // Admin who made the payment
  createdAt: string;
}

export interface AdvancePayment {
  id: string;
  staffId: string;
  staffName: string;
  staffPhone: string;
  amount: number;
  paymentDate: string;
  paymentMethod: 'cash' | 'bank_transfer' | 'upi' | 'cheque';
  reason?: string;
  notes?: string;
  status: 'active' | 'deducted' | 'cancelled';
  deductedFromSalaryId?: string; // Reference to salary record where this was deducted
  deductedDate?: string;
  paidBy: string;
  createdAt: string;
  updatedAt: string;
}

const SALARY_STORAGE_KEY = 'wok_ka_tadka_salary_records';
const PAYMENT_STORAGE_KEY = 'wok_ka_tadka_salary_payments';
const ADVANCE_STORAGE_KEY = 'wok_ka_tadka_advance_payments';

// Get all salary records from localStorage
export const getAllSalaryRecords = (): SalaryRecord[] => {
  try {
    const stored = localStorage.getItem(SALARY_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading salary records:', error);
    return [];
  }
};

// Save salary records to localStorage
export const saveSalaryRecords = (records: SalaryRecord[]): void => {
  try {
    localStorage.setItem(SALARY_STORAGE_KEY, JSON.stringify(records));
  } catch (error) {
    console.error('Error saving salary records:', error);
  }
};

// Get all salary payments from localStorage
export const getAllSalaryPayments = (): SalaryPayment[] => {
  try {
    const stored = localStorage.getItem(PAYMENT_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading salary payments:', error);
    return [];
  }
};

// Save salary payments to localStorage
export const saveSalaryPayments = (payments: SalaryPayment[]): void => {
  try {
    localStorage.setItem(PAYMENT_STORAGE_KEY, JSON.stringify(payments));
  } catch (error) {
    console.error('Error saving salary payments:', error);
  }
};

// Get current month in YYYY-MM format
export const getCurrentMonth = (): string => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
};

// Calculate salary based on attendance and base salary
export const calculateSalary = (
  baseSalary: number,
  workingDays: number,
  presentDays: number,
  totalHours: number,
  overtimeHours: number = 0,
  overtimeRate: number = 1.5,
  bonuses: number = 0,
  deductions: number = 0
): number => {
  // Calculate daily salary
  const dailySalary = baseSalary / workingDays;
  
  // Calculate base pay for present days
  const basePay = dailySalary * presentDays;
  
  // Calculate overtime pay (if any)
  const overtimePay = (baseSalary / (workingDays * 8)) * overtimeHours * overtimeRate;
  
  // Calculate final salary
  const finalSalary = basePay + overtimePay + bonuses - deductions;
  
  return Math.round(finalSalary);
};

// Create or update salary record
export const createSalaryRecord = (
  staffId: string,
  staffName: string,
  staffPhone: string,
  staffRole: string,
  baseSalary: number,
  workingDays: number,
  presentDays: number,
  totalHours: number,
  overtimeHours: number = 0,
  bonuses: number = 0,
  deductions: number = 0,
  month?: string
): SalaryRecord => {
  const records = getAllSalaryRecords();
  const currentMonth = month || getCurrentMonth();
  const year = parseInt(currentMonth.split('-')[0]);

  // Check if record already exists for this staff and month
  const existingIndex = records.findIndex(
    record => record.staffId === staffId && record.month === currentMonth
  );

  // Add active advances to deductions
  const activeAdvances = getTotalActiveAdvances(staffId);
  const totalDeductions = deductions + activeAdvances;

  const finalSalary = calculateSalary(
    baseSalary,
    workingDays,
    presentDays,
    totalHours,
    overtimeHours,
    1.5, // Default overtime rate
    bonuses,
    totalDeductions
  );

  const salaryRecord: SalaryRecord = {
    id: existingIndex >= 0 ? records[existingIndex].id : `salary-${staffId}-${currentMonth}`,
    staffId,
    staffName,
    staffPhone,
    staffRole,
    month: currentMonth,
    year,
    baseSalary,
    workingDays,
    presentDays,
    totalHours,
    overtimeHours,
    overtimeRate: 1.5,
    bonuses,
    deductions: totalDeductions,
    finalSalary,
    status: 'pending',
    paidAmount: existingIndex >= 0 ? records[existingIndex].paidAmount : 0,
    createdAt: existingIndex >= 0 ? records[existingIndex].createdAt : new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  if (existingIndex >= 0) {
    records[existingIndex] = { ...records[existingIndex], ...salaryRecord };
  } else {
    records.push(salaryRecord);

    // Mark active advances as deducted
    if (activeAdvances > 0) {
      const activeAdvanceRecords = getActiveAdvancesForStaff(staffId);
      activeAdvanceRecords.forEach(advance => {
        deductAdvanceFromSalary(advance.id, salaryRecord.id);
      });
    }
  }

  saveSalaryRecords(records);
  return salaryRecord;
};

// Get salary record for specific staff and month
export const getSalaryRecord = (staffId: string, month?: string): SalaryRecord | null => {
  const records = getAllSalaryRecords();
  const targetMonth = month || getCurrentMonth();
  return records.find(record => record.staffId === staffId && record.month === targetMonth) || null;
};

// Get salary record by ID
export const getSalaryRecordById = (recordId: string): SalaryRecord | null => {
  const records = getAllSalaryRecords();
  return records.find(record => record.id === recordId) || null;
};

// Get salary records for a specific staff member
export const getStaffSalaryRecords = (staffId: string, limit?: number): SalaryRecord[] => {
  const records = getAllSalaryRecords();
  const staffRecords = records
    .filter(record => record.staffId === staffId)
    .sort((a, b) => new Date(b.month).getTime() - new Date(a.month).getTime());

  return limit ? staffRecords.slice(0, limit) : staffRecords;
};

// Make salary payment
export const makeSalaryPayment = (
  salaryRecordId: string,
  amount: number,
  paymentMethod: SalaryPayment['paymentMethod'],
  paidBy: string,
  transactionId?: string,
  notes?: string
): SalaryPayment => {
  const records = getAllSalaryRecords();
  const payments = getAllSalaryPayments();
  
  // Find the salary record
  const recordIndex = records.findIndex(record => record.id === salaryRecordId);
  if (recordIndex === -1) {
    throw new Error('Salary record not found');
  }
  
  const salaryRecord = records[recordIndex];
  
  // Create payment record
  const payment: SalaryPayment = {
    id: `payment-${Date.now()}`,
    salaryRecordId,
    staffId: salaryRecord.staffId,
    amount,
    paymentDate: new Date().toISOString(),
    paymentMethod,
    paymentType: 'salary',
    transactionId,
    notes,
    paidBy,
    createdAt: new Date().toISOString()
  };
  
  // Update salary record
  const newPaidAmount = salaryRecord.paidAmount + amount;
  salaryRecord.paidAmount = newPaidAmount;
  salaryRecord.updatedAt = new Date().toISOString();
  
  if (newPaidAmount >= salaryRecord.finalSalary) {
    salaryRecord.status = 'paid';
    salaryRecord.paymentDate = new Date().toISOString();
    salaryRecord.paymentMethod = paymentMethod;
  } else if (newPaidAmount > 0) {
    salaryRecord.status = 'partial';
  }
  
  // Save updates
  records[recordIndex] = salaryRecord;
  payments.push(payment);
  
  saveSalaryRecords(records);
  saveSalaryPayments(payments);
  
  return payment;
};

// Get payments for a salary record
export const getPaymentsForSalaryRecord = (salaryRecordId: string): SalaryPayment[] => {
  const payments = getAllSalaryPayments();
  return payments.filter(payment => payment.salaryRecordId === salaryRecordId);
};

// Get all pending salary records
export const getPendingSalaryRecords = (): SalaryRecord[] => {
  const records = getAllSalaryRecords();
  return records.filter(record => record.status === 'pending' || record.status === 'partial');
};

// Format month for display
export const formatMonth = (monthString: string): string => {
  const [year, month] = monthString.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1);
  return date.toLocaleDateString('en-IN', { year: 'numeric', month: 'long' });
};

// Get all advance payments from localStorage
export const getAllAdvancePayments = (): AdvancePayment[] => {
  try {
    const stored = localStorage.getItem(ADVANCE_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading advance payments:', error);
    return [];
  }
};

// Save advance payments to localStorage
export const saveAdvancePayments = (advances: AdvancePayment[]): void => {
  try {
    localStorage.setItem(ADVANCE_STORAGE_KEY, JSON.stringify(advances));
  } catch (error) {
    console.error('Error saving advance payments:', error);
  }
};

// Create advance payment record
export const createAdvancePayment = (
  staffId: string,
  staffName: string,
  staffPhone: string,
  amount: number,
  paymentMethod: AdvancePayment['paymentMethod'],
  paidBy: string,
  reason?: string,
  notes?: string
): AdvancePayment => {
  const advances = getAllAdvancePayments();

  const newAdvance: AdvancePayment = {
    id: `advance-${Date.now()}`,
    staffId,
    staffName,
    staffPhone,
    amount,
    paymentDate: new Date().toISOString(),
    paymentMethod,
    reason,
    notes,
    status: 'active',
    paidBy,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  advances.push(newAdvance);
  saveAdvancePayments(advances);
  return newAdvance;
};

// Get active advances for a staff member
export const getActiveAdvancesForStaff = (staffId: string): AdvancePayment[] => {
  const advances = getAllAdvancePayments();
  return advances.filter(advance =>
    advance.staffId === staffId && advance.status === 'active'
  );
};

// Get total active advance amount for a staff member
export const getTotalActiveAdvances = (staffId: string): number => {
  const activeAdvances = getActiveAdvancesForStaff(staffId);
  return activeAdvances.reduce((sum, advance) => sum + advance.amount, 0);
};

// Mark advance as deducted from salary
export const deductAdvanceFromSalary = (
  advanceId: string,
  salaryRecordId: string
): boolean => {
  const advances = getAllAdvancePayments();
  const advanceIndex = advances.findIndex(advance => advance.id === advanceId);

  if (advanceIndex === -1) return false;

  advances[advanceIndex].status = 'deducted';
  advances[advanceIndex].deductedFromSalaryId = salaryRecordId;
  advances[advanceIndex].deductedDate = new Date().toISOString();
  advances[advanceIndex].updatedAt = new Date().toISOString();

  saveAdvancePayments(advances);
  return true;
};

// Get salary summary for admin dashboard
export const getSalarySummary = () => {
  const records = getAllSalaryRecords();
  const advances = getAllAdvancePayments();
  const currentMonth = getCurrentMonth();

  const currentMonthRecords = records.filter(record => record.month === currentMonth);
  const totalPending = currentMonthRecords.reduce((sum, record) =>
    sum + (record.finalSalary - record.paidAmount), 0
  );
  const totalPaid = currentMonthRecords.reduce((sum, record) => sum + record.paidAmount, 0);
  const totalActiveAdvances = advances
    .filter(advance => advance.status === 'active')
    .reduce((sum, advance) => sum + advance.amount, 0);

  return {
    totalStaff: currentMonthRecords.length,
    totalPending,
    totalPaid,
    totalActiveAdvances,
    pendingCount: currentMonthRecords.filter(r => r.status === 'pending' || r.status === 'partial').length,
    paidCount: currentMonthRecords.filter(r => r.status === 'paid').length
  };
};
