// Menu Status Manager for controlling menu item availability across apps

export interface MenuItemStatus {
  itemId: string;
  itemName: string;
  category: string;
  isDisabled: boolean;
  disabledOn: 'customer-app' | 'waiter-app' | 'both';
  reason: string;
  scheduledFrom?: string;
  scheduledTo?: string;
  disabledBy: string;
  disabledAt: string;
  customReason?: string;
}

export interface CategoryStatus {
  categoryName: string;
  isDisabled: boolean;
  disabledOn: 'customer-app' | 'waiter-app' | 'both';
  reason: string;
  disabledItemsCount: number;
  totalItemsCount: number;
}

class MenuStatusManager {
  private readonly STORAGE_KEY = 'menu_item_statuses';

  // Get all menu item statuses
  getAllStatuses(): MenuItemStatus[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error reading menu statuses from storage:', error);
      return [];
    }
  }

  // Save statuses to localStorage
  private saveStatuses(statuses: MenuItemStatus[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(statuses));
    } catch (error) {
      console.error('Error saving menu statuses to storage:', error);
    }
  }

  // Get status for a specific item
  getItemStatus(itemId: string): MenuItemStatus | null {
    const statuses = this.getAllStatuses();
    return statuses.find(status => status.itemId === itemId) || null;
  }

  // Check if item is available for a specific app
  isItemAvailable(itemId: string, appType: 'customer-app' | 'waiter-app'): boolean {
    const status = this.getItemStatus(itemId);
    
    if (!status || !status.isDisabled) {
      return true; // Item is available if no status or not disabled
    }

    // Check if item is disabled for this specific app
    if (status.disabledOn === 'both' || status.disabledOn === appType) {
      // Check if it's scheduled and still within the disable period
      if (status.scheduledFrom && status.scheduledTo) {
        const now = new Date();
        const from = new Date(status.scheduledFrom);
        const to = new Date(status.scheduledTo);
        
        // If current time is outside the scheduled period, item should be available
        if (now < from || now > to) {
          return true;
        }
      }
      
      return false; // Item is disabled for this app
    }

    return true; // Item is not disabled for this app
  }

  // Check if entire category is available for a specific app
  isCategoryAvailable(categoryName: string, appType: 'customer-app' | 'waiter-app'): boolean {
    const statuses = this.getAllStatuses();
    const categoryStatuses = statuses.filter(status => status.category === categoryName);
    
    // If no items in category are disabled, category is available
    if (categoryStatuses.length === 0) {
      return true;
    }

    // Check if all items in category are disabled for this app
    const disabledForApp = categoryStatuses.filter(status => 
      status.isDisabled && (status.disabledOn === 'both' || status.disabledOn === appType)
    );

    // Category is unavailable if all items are disabled
    return disabledForApp.length === 0;
  }

  // Disable a menu item
  disableItem(
    itemId: string,
    itemName: string,
    category: string,
    disabledOn: 'customer-app' | 'waiter-app' | 'both',
    reason: string,
    scheduledFrom?: string,
    scheduledTo?: string,
    customReason?: string
  ): boolean {
    try {
      const statuses = this.getAllStatuses();
      
      // Remove existing status for this item
      const filteredStatuses = statuses.filter(status => status.itemId !== itemId);
      
      // Add new status
      const newStatus: MenuItemStatus = {
        itemId,
        itemName,
        category,
        isDisabled: true,
        disabledOn,
        reason,
        scheduledFrom,
        scheduledTo,
        disabledBy: 'Admin', // In real app, get from auth context
        disabledAt: new Date().toISOString(),
        customReason
      };
      
      filteredStatuses.push(newStatus);
      this.saveStatuses(filteredStatuses);
      
      return true;
    } catch (error) {
      console.error('Error disabling item:', error);
      return false;
    }
  }

  // Enable a menu item
  enableItem(itemId: string): boolean {
    try {
      const statuses = this.getAllStatuses();
      const filteredStatuses = statuses.filter(status => status.itemId !== itemId);
      this.saveStatuses(filteredStatuses);
      return true;
    } catch (error) {
      console.error('Error enabling item:', error);
      return false;
    }
  }

  // Disable entire category
  disableCategory(
    categoryName: string,
    categoryItems: any[],
    disabledOn: 'customer-app' | 'waiter-app' | 'both',
    reason: string,
    scheduledFrom?: string,
    scheduledTo?: string,
    customReason?: string
  ): boolean {
    try {
      const statuses = this.getAllStatuses();
      
      // Remove existing statuses for items in this category
      const filteredStatuses = statuses.filter(status => status.category !== categoryName);
      
      // Add new statuses for all items in category
      const newStatuses: MenuItemStatus[] = categoryItems.map(item => ({
        itemId: item.id.toString(),
        itemName: item.name,
        category: categoryName,
        isDisabled: true,
        disabledOn,
        reason,
        scheduledFrom,
        scheduledTo,
        disabledBy: 'Admin',
        disabledAt: new Date().toISOString(),
        customReason
      }));
      
      filteredStatuses.push(...newStatuses);
      this.saveStatuses(filteredStatuses);
      
      return true;
    } catch (error) {
      console.error('Error disabling category:', error);
      return false;
    }
  }

  // Enable entire category
  enableCategory(categoryName: string): boolean {
    try {
      const statuses = this.getAllStatuses();
      const filteredStatuses = statuses.filter(status => status.category !== categoryName);
      this.saveStatuses(filteredStatuses);
      return true;
    } catch (error) {
      console.error('Error enabling category:', error);
      return false;
    }
  }

  // Get filtered menu data based on app type and availability
  getAvailableMenuData(menuData: any, appType: 'customer-app' | 'waiter-app'): any {
    const availableMenuData: any = {};

    Object.keys(menuData).forEach(categoryName => {
      const categoryItems = menuData[categoryName];
      const availableItems = categoryItems.filter((item: any) =>
        this.isItemAvailable(item.id.toString(), appType)
      );

      // Only include category if it has available items
      if (availableItems.length > 0) {
        availableMenuData[categoryName] = availableItems;
      }
    });

    return availableMenuData;
  }

  // Get disabled items summary
  getDisabledItemsSummary(): {
    totalDisabled: number;
    customerAppOnly: number;
    waiterAppOnly: number;
    bothApps: number;
    byReason: { [reason: string]: number };
  } {
    const statuses = this.getAllStatuses();
    const activeDisabled = statuses.filter(status => status.isDisabled);
    
    const summary = {
      totalDisabled: activeDisabled.length,
      customerAppOnly: activeDisabled.filter(s => s.disabledOn === 'customer-app').length,
      waiterAppOnly: activeDisabled.filter(s => s.disabledOn === 'waiter-app').length,
      bothApps: activeDisabled.filter(s => s.disabledOn === 'both').length,
      byReason: {} as { [reason: string]: number }
    };
    
    // Count by reason
    activeDisabled.forEach(status => {
      const reason = status.reason;
      summary.byReason[reason] = (summary.byReason[reason] || 0) + 1;
    });
    
    return summary;
  }

  // Clean up expired scheduled disables
  cleanupExpiredSchedules(): number {
    const statuses = this.getAllStatuses();
    const now = new Date();
    let cleanedCount = 0;
    
    const activeStatuses = statuses.filter(status => {
      if (status.scheduledTo) {
        const endTime = new Date(status.scheduledTo);
        if (now > endTime) {
          cleanedCount++;
          return false; // Remove expired status
        }
      }
      return true; // Keep active status
    });
    
    if (cleanedCount > 0) {
      this.saveStatuses(activeStatuses);
    }
    
    return cleanedCount;
  }

  // Clear all statuses (for testing/reset)
  clearAllStatuses(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }
}

// Export singleton instance
export const menuStatusManager = new MenuStatusManager();
