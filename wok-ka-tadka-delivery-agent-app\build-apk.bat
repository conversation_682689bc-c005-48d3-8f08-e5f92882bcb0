@echo off
echo Building Wok Ka Tadka Delivery App APK...
echo.

echo Step 1: Building web application...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Web build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Syncing with Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo Error: Capacitor sync failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Building Android APK...
cd android
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo.
    echo Error: APK build failed!
    echo This usually means Android SDK is not installed or configured.
    echo Please install Android Studio and configure the SDK path.
    echo See BUILD_INSTRUCTIONS.md for detailed setup instructions.
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ APK built successfully!
echo Location: android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo You can now install this APK on your Android device.
echo.
pause
