import { useEffect } from 'react';
import { StatusBar, Style } from '@capacitor/status-bar';
import { Capacitor } from '@capacitor/core';

export function useStatusBar() {
  useEffect(() => {
    const setupStatusBar = async () => {
      if (Capacitor.isNativePlatform()) {
        try {
          // Show status bar and make it work properly with content
          await StatusBar.show();
          await StatusBar.setStyle({ style: Style.Light });
          await StatusBar.setBackgroundColor({ color: '#ef4444' });
          // This is key - don't overlay the webview
          await StatusBar.setOverlaysWebView({ overlay: false });

        } catch (error) {
          console.log('Status bar setup failed:', error);
        }
      }
    };

    setupStatusBar();
  }, []);
}
