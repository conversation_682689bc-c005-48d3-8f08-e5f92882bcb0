// Sample customer data generator for testing
import { customerManager } from './customerStorage';

const sampleCustomers = [
  {
    name: "<PERSON><PERSON>",
    phone: "+91 9876543210",
    email: "r<PERSON><PERSON><PERSON><EMAIL>",
    address: "123 MG Road, Bandra West, Mumbai - 400050",
    notes: "Prefers less spicy food"
  },
  {
    name: "<PERSON><PERSON>",
    phone: "+91 9876543211",
    email: "<EMAIL>",
    address: "456 Park Street, Andheri East, Mumbai - 400069",
    notes: "Regular customer, loves biryani"
  },
  {
    name: "<PERSON><PERSON> <PERSON>",
    phone: "+91 9876543212",
    email: "<EMAIL>",
    address: "789 SV Road, Malad West, Mumbai - 400064",
    notes: "Vegetarian only"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    phone: "+91 9876543213",
    email: "<EMAIL>",
    address: "321 Hill Road, Bandra West, Mumbai - 400050",
    notes: "Allergic to nuts"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    phone: "+91 9876543214",
    email: "<EMAIL>",
    address: "654 Linking Road, Khar West, Mumbai - 400052",
    notes: "Prefers extra spicy"
  },
  {
    name: "<PERSON>",
    phone: "+91 9876543215",
    email: "<EMAIL>",
    address: "987 Turner Road, Bandra West, Mumbai - 400050",
    notes: "Corporate client"
  },
  {
    name: "Rohit Mehta",
    phone: "+91 9876543216",
    email: "<EMAIL>",
    address: "147 Carter Road, Bandra West, Mumbai - 400050",
    notes: "Family of 4, regular weekend visits"
  },
  {
    name: "Kavya Nair",
    phone: "+91 **********",
    email: "<EMAIL>",
    address: "258 Juhu Tara Road, Juhu, Mumbai - 400049",
    notes: "South Indian preferences"
  },
  {
    name: "Arjun Reddy",
    phone: "+91 **********",
    email: "<EMAIL>",
    address: "369 Versova Link Road, Versova, Mumbai - 400061",
    notes: "Prefers tandoori items"
  },
  {
    name: "Meera Joshi",
    phone: "+91 **********",
    email: "<EMAIL>",
    address: "741 Lokhandwala Complex, Andheri West, Mumbai - 400053",
    notes: "Health conscious, prefers grilled items"
  }
];

// Sample order data to make some customers regular
const sampleOrders = [
  { customerName: "Rajesh Kumar", orders: 5, totalSpent: 2500 },
  { customerName: "Priya Sharma", orders: 8, totalSpent: 4200 },
  { customerName: "Amit Patel", orders: 3, totalSpent: 1800 },
  { customerName: "Sneha Gupta", orders: 6, totalSpent: 3100 },
  { customerName: "Vikram Singh", orders: 4, totalSpent: 2200 },
  { customerName: "Anita Desai", orders: 12, totalSpent: 6800 },
  { customerName: "Rohit Mehta", orders: 7, totalSpent: 4500 }
];

export function initializeSampleCustomers(): void {
  // Clear existing customers
  customerManager.clearAllData();
  
  console.log('Initializing sample customers...');
  
  // Add sample customers
  const addedCustomers = sampleCustomers.map(customerData => {
    return customerManager.addCustomer(customerData);
  });
  
  console.log(`Added ${addedCustomers.length} sample customers`);
  
  // Simulate order history for some customers to make them regular
  sampleOrders.forEach(orderData => {
    const customer = customerManager.getCustomerByName(orderData.customerName);
    if (customer) {
      // Simulate multiple orders
      for (let i = 0; i < orderData.orders; i++) {
        const orderAmount = Math.floor(orderData.totalSpent / orderData.orders);
        const kotNumber = `KOT${Date.now()}${i}`;
        
        customerManager.addCustomerOrder({
          customerId: customer.id,
          orderId: kotNumber,
          tableId: `Table ${Math.floor(Math.random() * 8) + 1}`,
          kotNumber: kotNumber,
          amount: orderAmount,
          items: [
            { name: "Sample Item 1", quantity: 1, price: Math.floor(orderAmount * 0.6) },
            { name: "Sample Item 2", quantity: 1, price: Math.floor(orderAmount * 0.4) }
          ]
        });
        
        // Add some delay between orders to simulate realistic timestamps
        const pastDate = new Date();
        pastDate.setDate(pastDate.getDate() - Math.floor(Math.random() * 30));
      }
    }
  });
  
  console.log('Sample customer data initialized successfully!');
  
  // Log statistics
  const stats = customerManager.getCustomerStats();
  console.log('Customer Statistics:', stats);
}

export function clearSampleCustomers(): void {
  customerManager.clearAllData();
  console.log('Sample customer data cleared');
}

// Initialize sample customers if in development mode
if (process.env.NODE_ENV === 'development') {
  // Auto-initialize on first load if no customers exist
  const existingCustomers = customerManager.getAllCustomers();
  if (existingCustomers.length === 0) {
    console.log('No existing customers found, initializing sample data...');
    initializeSampleCustomers();
  }
}
