
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";

const Index = () => {
  const navigate = useNavigate();
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Show splash screen for 3 seconds, then redirect
    const timer = setTimeout(() => {
      setShowSplash(false);
      navigate("/delivery/login");
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigate]);

  if (showSplash) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
        {/* Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-400 via-red-500 to-red-600"></div>

        {/* Background Decorative Elements */}
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-red-300 rounded-full opacity-20 -mb-16 -ml-16"></div>
        <div className="absolute bottom-20 right-10 w-20 h-20 bg-red-300 rounded-full opacity-15"></div>
        <div className="absolute top-20 left-10 w-16 h-16 bg-red-300 rounded-full opacity-10"></div>

        {/* Main Content */}
        <div className="relative z-10 flex flex-col items-center text-center px-8">
          {/* Logo Container with Glass Effect */}
          <div className="mb-12 p-6 rounded-3xl bg-white/20 backdrop-blur-sm border border-white/30 shadow-2xl">
            <Logo size="xl" variant="default" />
          </div>

          {/* App Title */}
          <h1 className="text-4xl font-bold text-white mb-4 tracking-wide">
            Wok Ka Tadka
          </h1>

          {/* Subtitle */}
          <p className="text-xl text-white/90 mb-2 font-medium">
            Mumbai Style Chinese & Indian
          </p>

          {/* Features */}
          <div className="flex items-center space-x-2 mb-12 text-white/80">
            <span className="text-green-300">★</span>
            <span className="text-sm">Authentic</span>
            <span className="text-white/60">•</span>
            <span className="text-sm">Fresh</span>
            <span className="text-white/60">•</span>
            <span className="text-sm">Delicious</span>
            <span className="text-green-300">★</span>
          </div>

          {/* Loading Indicator */}
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin mb-4"></div>
            <p className="text-white/80 text-sm">Loading delicious experience...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-primary">
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        <p>Redirecting to Wok Ka Tadka Delivery Portal...</p>
      </div>
    </div>
  );
};

export default Index;
