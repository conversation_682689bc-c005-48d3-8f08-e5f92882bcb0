
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, MapPin, Clock, Calendar, Filter, SlidersHorizontal } from "lucide-react";

const History = () => {
  const navigate = useNavigate();
  const [filter, setFilter] = useState("all");
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const deliveryHistory = [
    {
      id: "#56970",
      customerName: "<PERSON><PERSON>",
      address: "Sector 18, Noida",
      amount: 345,
      status: "delivered",
      date: "Today",
      time: "2:30 PM"
    },
    {
      id: "#56969",
      customerName: "<PERSON><PERSON> Gupta",
      address: "Connaught Place, Delhi",
      amount: 520,
      status: "delivered",
      date: "Today",
      time: "1:45 PM"
    },
    {
      id: "#56968",
      customerName: "<PERSON><PERSON><PERSON>",
      address: "Gurgaon Sector 47",
      amount: 280,
      status: "delivered",
      date: "Yesterday",
      time: "7:20 PM"
    },
    {
      id: "#56967",
      customerName: "Vikram Mehta",
      address: "Lajpat Nagar, Delhi",
      amount: 410,
      status: "cancelled",
      date: "Yesterday",
      time: "6:15 PM"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered": return "bg-success text-success-foreground";
      case "cancelled": return "bg-destructive text-destructive-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "delivered": return "Delivered";
      case "cancelled": return "Cancelled";
      default: return status;
    }
  };

  const getFilteredHistory = () => {
    switch (filter) {
      case "today":
        return deliveryHistory.filter(order => order.date === "Today");
      case "week":
        return deliveryHistory.filter(order => order.date === "Today" || order.date === "Yesterday");
      case "delivered":
        return deliveryHistory.filter(order => order.status === "delivered");
      case "cancelled":
        return deliveryHistory.filter(order => order.status === "cancelled");
      case "high-value":
        return deliveryHistory.filter(order => order.amount >= 500);
      case "recent":
        return deliveryHistory.filter(order => order.date === "Today");
      default:
        return deliveryHistory;
    }
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">Delivery History</h1>
              <p className="text-white/80 text-sm">Your completed deliveries</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className={`text-white hover:bg-white/20 transition-all duration-200 ${
              showAdvancedFilters ? 'bg-white/20' : ''
            }`}
          >
            <SlidersHorizontal className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-4 apk-content-with-header">
        {/* Filter Tabs */}
        <Card className="bg-white shadow-sm border-0 mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-3 gap-3">
              <Button
                variant={filter === "all" ? "delivery" : "outline"}
                size="sm"
                onClick={() => setFilter("all")}
                className="w-full rounded-full text-sm font-medium"
              >
                All
              </Button>
              <Button
                variant={filter === "today" ? "delivery" : "outline"}
                size="sm"
                onClick={() => setFilter("today")}
                className="w-full rounded-full text-sm font-medium"
              >
                Today
              </Button>
              <Button
                variant={filter === "week" ? "delivery" : "outline"}
                size="sm"
                onClick={() => setFilter("week")}
                className="w-full rounded-full text-sm font-medium"
              >
                This Week
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <Card className="bg-white shadow-sm border-0 mb-6">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Advanced Filters</h3>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-sm"
                  onClick={() => setFilter("delivered")}
                >
                  Delivered Only
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-sm"
                  onClick={() => setFilter("cancelled")}
                >
                  Cancelled Only
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-sm"
                  onClick={() => setFilter("high-value")}
                >
                  High Value (₹500+)
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-sm"
                  onClick={() => setFilter("recent")}
                >
                  Last 24 Hours
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* History Cards */}
        {getFilteredHistory().length === 0 ? (
          <Card className="shadow-sm border-0 bg-white">
            <CardContent className="p-8 text-center">
              <Filter className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg font-medium">No deliveries found</p>
              <p className="text-gray-400 text-sm mt-2">Try adjusting your filters to see more results</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setFilter("all");
                  setShowAdvancedFilters(false);
                }}
                className="mt-4"
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        ) : (
          getFilteredHistory().map((order) => (
            <Card key={order.id} className="shadow-sm border-0 bg-white hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-gray-900">{order.id}</span>
                    <Badge className={`${getStatusColor(order.status)} text-xs px-2 py-1`}>
                      {getStatusText(order.status)}
                    </Badge>
                  </div>
                  <p className="font-bold text-lg text-primary">₹{order.amount}</p>
                </div>

                <div className="space-y-2">
                  <p className="font-semibold text-gray-900">{order.customerName}</p>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{order.address}</span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{order.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{order.time}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default History;
