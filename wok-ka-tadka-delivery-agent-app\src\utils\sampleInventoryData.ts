import { addInventoryItem, getAllInventoryItems } from './inventoryStorage';

export const sampleInventoryData = [
  {
    name: "Basmati Rice",
    category: "grains" as const,
    currentStock: 25,
    unit: "kg" as const,
    minStockLevel: 20,
    maxStockLevel: 100,
    costPerUnit: 120,
    supplier: "Rice World Ltd",
    expiryDate: "2024-12-31",
    notes: "Premium quality basmati rice for biryani"
  },
  {
    name: "Chicken Breast",
    category: "meat" as const,
    currentStock: 8,
    unit: "kg" as const,
    minStockLevel: 15,
    maxStockLevel: 50,
    costPerUnit: 280,
    supplier: "Fresh Meat Co",
    expiryDate: "2024-02-05",
    notes: "Fresh chicken for curries and biryani"
  },
  {
    name: "Paneer",
    category: "dairy" as const,
    currentStock: 3,
    unit: "kg" as const,
    minStockLevel: 10,
    maxStockLevel: 25,
    costPerUnit: 350,
    supplier: "Dairy Fresh",
    expiryDate: "2024-02-03",
    notes: "Fresh paneer for vegetarian dishes"
  },
  {
    name: "Onions",
    category: "vegetables" as const,
    currentStock: 45,
    unit: "kg" as const,
    minStockLevel: 20,
    maxStockLevel: 80,
    costPerUnit: 40,
    supplier: "Veggie Market",
    notes: "Essential ingredient for most dishes"
  },
  {
    name: "Tomatoes",
    category: "vegetables" as const,
    currentStock: 12,
    unit: "kg" as const,
    minStockLevel: 15,
    maxStockLevel: 40,
    costPerUnit: 60,
    supplier: "Veggie Market",
    expiryDate: "2024-02-08",
    notes: "Fresh tomatoes for gravies and salads"
  },
  {
    name: "Garam Masala",
    category: "spices" as const,
    currentStock: 2,
    unit: "kg" as const,
    minStockLevel: 3,
    maxStockLevel: 10,
    costPerUnit: 800,
    supplier: "Spice Garden",
    notes: "Premium blend of spices"
  },
  {
    name: "Cooking Oil",
    category: "other" as const,
    currentStock: 18,
    unit: "liters" as const,
    minStockLevel: 10,
    maxStockLevel: 50,
    costPerUnit: 150,
    supplier: "Oil Mills",
    notes: "Refined sunflower oil for cooking"
  },
  {
    name: "Milk",
    category: "dairy" as const,
    currentStock: 6,
    unit: "liters" as const,
    minStockLevel: 10,
    maxStockLevel: 30,
    costPerUnit: 55,
    supplier: "Dairy Fresh",
    expiryDate: "2024-02-02",
    notes: "Fresh milk for tea, coffee and desserts"
  },
  {
    name: "Coriander Leaves",
    category: "vegetables" as const,
    currentStock: 1,
    unit: "kg" as const,
    minStockLevel: 2,
    maxStockLevel: 8,
    costPerUnit: 120,
    supplier: "Herb Farm",
    expiryDate: "2024-02-01",
    notes: "Fresh coriander for garnishing"
  },
  {
    name: "Yogurt",
    category: "dairy" as const,
    currentStock: 8,
    unit: "kg" as const,
    minStockLevel: 5,
    maxStockLevel: 20,
    costPerUnit: 80,
    supplier: "Dairy Fresh",
    expiryDate: "2024-02-06",
    notes: "Fresh yogurt for raita and marinades"
  },
  {
    name: "Mutton",
    category: "meat" as const,
    currentStock: 4,
    unit: "kg" as const,
    minStockLevel: 8,
    maxStockLevel: 25,
    costPerUnit: 650,
    supplier: "Premium Meat",
    expiryDate: "2024-02-04",
    notes: "Fresh mutton for special dishes"
  },
  {
    name: "Green Chilies",
    category: "vegetables" as const,
    currentStock: 0.5,
    unit: "kg" as const,
    minStockLevel: 1,
    maxStockLevel: 5,
    costPerUnit: 100,
    supplier: "Veggie Market",
    expiryDate: "2024-02-10",
    notes: "Fresh green chilies for heat"
  },
  {
    name: "Cumin Seeds",
    category: "spices" as const,
    currentStock: 1.2,
    unit: "kg" as const,
    minStockLevel: 1,
    maxStockLevel: 5,
    costPerUnit: 400,
    supplier: "Spice Garden",
    notes: "Whole cumin seeds for tempering"
  },
  {
    name: "Coca Cola",
    category: "beverages" as const,
    currentStock: 24,
    unit: "bottles" as const,
    minStockLevel: 20,
    maxStockLevel: 100,
    costPerUnit: 35,
    supplier: "Beverage Distributor",
    notes: "Cold drinks for customers"
  },
  {
    name: "Mineral Water",
    category: "beverages" as const,
    currentStock: 48,
    unit: "bottles" as const,
    minStockLevel: 30,
    maxStockLevel: 150,
    costPerUnit: 20,
    supplier: "Water Co",
    notes: "Packaged drinking water"
  }
];

export const populateSampleData = () => {
  // Check if data already exists
  const existingItems = getAllInventoryItems();
  if (existingItems.length > 0) {
    console.log('Sample data already exists, skipping population');
    return;
  }

  console.log('Populating sample inventory data...');
  
  sampleInventoryData.forEach(item => {
    try {
      addInventoryItem({
        ...item,
        purchaseDate: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`Failed to add ${item.name}:`, error);
    }
  });

  console.log('Sample inventory data populated successfully!');
};

// Auto-populate on import (only if no data exists)
if (typeof window !== 'undefined') {
  // Only run in browser environment
  setTimeout(() => {
    const existingItems = getAllInventoryItems();
    if (existingItems.length === 0) {
      populateSampleData();
    }
  }, 1000);
}
