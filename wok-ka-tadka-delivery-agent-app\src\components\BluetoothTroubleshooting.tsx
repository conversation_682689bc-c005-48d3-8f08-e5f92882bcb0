import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Bluetooth, 
  Wifi, 
  Usb, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Smartphone,
  Monitor,
  Settings,
  Power,
  RefreshCw
} from "lucide-react";

interface BluetoothTroubleshootingProps {
  onClose?: () => void;
}

const BluetoothTroubleshooting: React.FC<BluetoothTroubleshootingProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const troubleshootingSteps = [
    {
      title: "Check Bluetooth Support",
      description: "Verify your device supports Bluetooth printing",
      icon: <Bluetooth className="h-5 w-5" />,
      test: async () => {
        return 'bluetooth' in navigator;
      }
    },
    {
      title: "Enable Bluetooth",
      description: "Make sure Bluetooth is enabled on your device",
      icon: <Settings className="h-5 w-5" />,
      test: async () => {
        try {
          const available = await navigator.bluetooth?.getAvailability();
          return available || false;
        } catch {
          return false;
        }
      }
    },
    {
      title: "Printer Power & Pairing Mode",
      description: "Ensure your Everycom EC58 is powered on and in pairing mode",
      icon: <Power className="h-5 w-5" />,
      manual: true
    },
    {
      title: "Windows Bluetooth Pairing",
      description: "Pair the printer in Windows Bluetooth settings first",
      icon: <Monitor className="h-5 w-5" />,
      manual: true
    },
    {
      title: "Test Web Bluetooth",
      description: "Test if Web Bluetooth can detect your printer",
      icon: <RefreshCw className="h-5 w-5" />,
      test: async () => {
        try {
          const device = await navigator.bluetooth?.requestDevice({
            filters: [
              { namePrefix: 'EC58' },
              { namePrefix: 'Everycom' },
              { namePrefix: 'Thermal' }
            ],
            optionalServices: ['000018f0-0000-1000-8000-00805f9b34fb']
          });
          return !!device;
        } catch {
          return false;
        }
      }
    }
  ];

  const runTest = async (stepIndex: number) => {
    const step = troubleshootingSteps[stepIndex];
    if (step.test) {
      try {
        const result = await step.test();
        setTestResults(prev => ({ ...prev, [stepIndex]: result }));
      } catch (error) {
        setTestResults(prev => ({ ...prev, [stepIndex]: false }));
      }
    }
  };

  const runAllTests = async () => {
    for (let i = 0; i < troubleshootingSteps.length; i++) {
      if (troubleshootingSteps[i].test) {
        await runTest(i);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bluetooth className="h-6 w-6 text-blue-600" />
            Everycom EC58 Bluetooth Troubleshooting
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Quick Status */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Issue:</strong> Bluetooth shows "Driver is unavailable" for thermal printer.
              <br />
              <strong>Solution:</strong> Follow the steps below to resolve Bluetooth connectivity.
            </AlertDescription>
          </Alert>

          {/* Connection Methods */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4 text-center">
                <Usb className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-semibold text-green-800">USB (Working)</h3>
                <p className="text-sm text-green-600">Driver installed, printing works</p>
                <Badge variant="outline" className="mt-2 border-green-600 text-green-600">
                  ✓ Recommended
                </Badge>
              </CardContent>
            </Card>

            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="p-4 text-center">
                <Bluetooth className="h-8 w-8 mx-auto mb-2 text-yellow-600" />
                <h3 className="font-semibold text-yellow-800">Bluetooth (Issue)</h3>
                <p className="text-sm text-yellow-600">Driver unavailable</p>
                <Badge variant="outline" className="mt-2 border-yellow-600 text-yellow-600">
                  ⚠ Troubleshoot
                </Badge>
              </CardContent>
            </Card>

            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-4 text-center">
                <Smartphone className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-semibold text-blue-800">Mobile App</h3>
                <p className="text-sm text-blue-600">Enhanced mobile support</p>
                <Badge variant="outline" className="mt-2 border-blue-600 text-blue-600">
                  📱 Future
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Driver Unavailable Fix */}
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-semibold text-orange-800">🔧 Fix "Driver is unavailable" Error</p>
                <p className="text-sm text-orange-700">This is the most common issue with Everycom EC58 Bluetooth connectivity!</p>
                <ol className="text-sm text-orange-700 list-decimal list-inside space-y-1 mt-2">
                  <li>Download and install the official Everycom EC58 driver from manufacturer</li>
                  <li>Restart your computer after driver installation</li>
                  <li>Remove the printer from Bluetooth settings if already paired</li>
                  <li>Put printer in pairing mode again (see instructions below)</li>
                  <li>Re-pair the printer in Windows Bluetooth settings</li>
                  <li>Windows should now recognize it as a "Serial Port" device</li>
                </ol>
                <div className="mt-2 p-2 bg-orange-100 rounded text-xs text-orange-600">
                  <strong>Alternative:</strong> Try installing "Generic Bluetooth Radio" driver or update Bluetooth adapter drivers in Device Manager.
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Troubleshooting Steps */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Troubleshooting Steps</h3>
              <Button onClick={runAllTests} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Run All Tests
              </Button>
            </div>

            {troubleshootingSteps.map((step, index) => (
              <Card key={index} className="border-l-4 border-l-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="mt-1">{step.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-semibold">{step.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                        
                        {/* Step-specific instructions */}
                        {index === 2 && (
                          <div className="mt-2 p-3 bg-yellow-50 rounded border">
                            <p className="text-sm font-medium text-yellow-800">Everycom EC58 Pairing Mode:</p>
                            <ol className="text-sm text-yellow-700 mt-1 list-decimal list-inside space-y-1">
                              <li>Turn on your Everycom EC58 printer</li>
                              <li>Hold the power button for 3-5 seconds until LED blinks rapidly</li>
                              <li>The printer will show "BT" or Bluetooth symbol on display</li>
                              <li>LED should blink blue indicating pairing mode</li>
                              <li>Printer name will appear as "EC58", "Everycom", or "BT58"</li>
                            </ol>
                            <div className="mt-2 p-2 bg-yellow-100 rounded">
                              <p className="text-xs text-yellow-600">
                                <strong>Note:</strong> If pairing mode doesn't activate, try holding power + feed button together for 5 seconds.
                              </p>
                            </div>
                          </div>
                        )}

                        {index === 3 && (
                          <div className="mt-2 p-3 bg-blue-50 rounded border">
                            <p className="text-sm font-medium text-blue-800">Windows Bluetooth Pairing (Critical Step):</p>
                            <ol className="text-sm text-blue-700 mt-1 list-decimal list-inside space-y-1">
                              <li>Put Everycom EC58 in pairing mode first (see step above)</li>
                              <li>Go to Windows Settings → Bluetooth & devices</li>
                              <li>Click "Add device" → Bluetooth</li>
                              <li>Look for "EC58", "Everycom", or "BT58" in the list</li>
                              <li>Click on the printer name to pair</li>
                              <li>If prompted for PIN, try: 0000, 1234, or 1111</li>
                              <li>Wait for "Connected" status</li>
                              <li>Install any prompted drivers automatically</li>
                            </ol>
                            <div className="mt-2 p-2 bg-blue-100 rounded">
                              <p className="text-xs text-blue-600">
                                <strong>Important:</strong> The printer must be paired in Windows settings BEFORE using Web Bluetooth API.
                                This installs the necessary SPP (Serial Port Profile) drivers.
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {step.test && (
                        <Button 
                          onClick={() => runTest(index)} 
                          variant="outline" 
                          size="sm"
                        >
                          Test
                        </Button>
                      )}
                      
                      {testResults[index] !== undefined && (
                        testResults[index] ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Solutions */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="text-green-800">Recommended Solutions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium">Continue using USB connection</p>
                  <p className="text-sm text-green-700">Your USB connection is working perfectly. This is the most reliable option.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium">Install Bluetooth drivers manually</p>
                  <p className="text-sm text-green-700">Download and install specific Bluetooth drivers for Everycom EC58 from the manufacturer.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium">Use mobile app for Bluetooth</p>
                  <p className="text-sm text-green-700">Mobile devices often have better Bluetooth printer support than desktop browsers.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Mobile App Info */}
          <Alert>
            <Smartphone className="h-4 w-4" />
            <AlertDescription>
              <strong>Mobile App Support:</strong> Your restaurant management system includes enhanced mobile thermal printer support. 
              When you convert this to a mobile app, Bluetooth printing will work more reliably on phones and tablets.
            </AlertDescription>
          </Alert>

          {onClose && (
            <div className="flex justify-end">
              <Button onClick={onClose} variant="outline">
                Close
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BluetoothTroubleshooting;
