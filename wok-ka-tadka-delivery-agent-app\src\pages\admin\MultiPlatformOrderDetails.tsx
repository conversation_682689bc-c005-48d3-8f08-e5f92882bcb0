import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  ArrowLeft, 
  Package, 
  User, 
  MapPin, 
  Clock, 
  Phone, 
  CreditCard,
  CheckCircle,
  XCircle,
  AlertCircle,
  Utensils
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  MultiPlatformOrder,
  OrderStatus,
  multiPlatformOrderManager
} from '@/utils/multiPlatformOrders';
import MultiPlatformNotifications from '@/components/MultiPlatformNotifications';

const MultiPlatformOrderDetails = () => {
  const navigate = useNavigate();
  const { orderId } = useParams();
  const { toast } = useToast();
  const [order, setOrder] = useState<MultiPlatformOrder | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (orderId) {
      loadOrder();
    }
  }, [orderId]);

  const loadOrder = () => {
    const allOrders = multiPlatformOrderManager.getAllOrders();
    const foundOrder = allOrders.find(o => o.id === orderId);
    setOrder(foundOrder || null);
  };

  const handleStatusUpdate = async (newStatus: OrderStatus) => {
    if (!order) return;

    setIsUpdating(true);
    try {
      const success = multiPlatformOrderManager.updateOrderStatus(order.id, newStatus);
      if (success) {
        loadOrder(); // Reload order to get updated data
        toast({
          title: "Status Updated",
          description: `Order status changed to ${newStatus}`,
        });
      } else {
        throw new Error('Failed to update status');
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update order status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'own-app':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'new':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'preparing':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'picked-up':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'delivered':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'cancelled':
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case 'new':
        return <AlertCircle className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'preparing':
        return <Clock className="h-4 w-4" />;
      case 'ready':
        return <Package className="h-4 w-4" />;
      case 'picked-up':
        return <Package className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'own-app':
        return '🏪';
      default:
        return '📱';
    }
  };

  const getAvailableStatuses = (currentStatus: OrderStatus): OrderStatus[] => {
    switch (currentStatus) {
      case 'new':
        return ['accepted', 'rejected'];
      case 'accepted':
        return ['preparing', 'cancelled'];
      case 'preparing':
        return ['ready', 'cancelled'];
      case 'ready':
        return ['picked-up', 'cancelled'];
      case 'picked-up':
        return ['delivered', 'cancelled'];
      default:
        return [];
    }
  };

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Order Not Found</h3>
            <p className="text-gray-600 mb-4">The requested order could not be found.</p>
            <Button onClick={() => navigate('/admin/dashboard')}>
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b apk-header">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/admin/dashboard')}
                className="shrink-0"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900">
                  Order #{order.platformOrderId}
                </h1>
                <p className="text-xs sm:text-sm text-gray-600">
                  {getPlatformIcon(order.platform)} {order.platform.toUpperCase()} Order Details
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <MultiPlatformNotifications
                currentPage="multi-platform-order-details"
                onNotificationClick={() => navigate('/admin/dashboard')}
              />
              <Badge className={`${getPlatformColor(order.platform)} text-xs`}>
                {order.platform.toUpperCase()}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 apk-content-with-header space-y-4 sm:space-y-6">
        {/* Order Status Card */}
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <div className="text-2xl">
                  {getPlatformIcon(order.platform)}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">#{order.platformOrderId}</h2>
                  <p className="text-sm text-gray-600">
                    {order.delivery.type} • Placed at {new Date(order.timestamps.orderPlaced).toLocaleString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                  {getStatusIcon(order.status)}
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </Badge>
                {getAvailableStatuses(order.status).length > 0 && (
                  <Select
                    value={order.status}
                    onValueChange={(value) => handleStatusUpdate(value as OrderStatus)}
                    disabled={isUpdating}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={order.status} disabled>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </SelectItem>
                      {getAvailableStatuses(order.status).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-medium">{order.customer.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Phone</p>
                <div className="flex items-center gap-2">
                  <p className="font-medium">{order.customer.phone}</p>
                  <Button variant="outline" size="sm">
                    <Phone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {order.customer.email && (
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="font-medium">{order.customer.email}</p>
                </div>
              )}
              {order.delivery.type === 'delivery' && order.customer.address && (
                <div>
                  <p className="text-sm text-gray-600">Delivery Address</p>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 mt-1 text-gray-400" />
                    <div>
                      <p className="font-medium">{order.customer.address.street}</p>
                      <p className="text-sm text-gray-600">
                        {order.customer.address.area}, {order.customer.address.city} - {order.customer.address.pincode}
                      </p>
                      {order.customer.address.landmark && (
                        <p className="text-sm text-gray-500">Near {order.customer.address.landmark}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Order Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(order.timestamps)
                  .filter(([_, timestamp]) => timestamp)
                  .map(([status, timestamp]) => (
                    <div key={status} className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        status === order.status ? 'bg-blue-500' : 'bg-gray-300'
                      }`} />
                      <div className="flex-1">
                        <p className="font-medium capitalize">{status.replace(/([A-Z])/g, ' $1').trim()}</p>
                        <p className="text-sm text-gray-600">
                          {new Date(timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Utensils className="h-5 w-5" />
              Order Items ({order.items.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {order.items.map((item, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 truncate">{item.name}</h4>
                    <p className="text-sm text-gray-600">₹{item.price} × {item.quantity}</p>
                    {item.customizations && item.customizations.length > 0 && (
                      <p className="text-xs text-blue-600">
                        Customizations: {item.customizations.join(', ')}
                      </p>
                    )}
                    {item.specialInstructions && (
                      <p className="text-xs text-orange-600">
                        Note: {item.specialInstructions}
                      </p>
                    )}
                  </div>
                  <div className="text-right shrink-0">
                    <p className="font-semibold text-gray-900">₹{item.total}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Payment & Pricing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment & Pricing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span>₹{order.pricing.subtotal}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Taxes</span>
                <span>₹{order.pricing.taxes}</span>
              </div>
              {order.pricing.deliveryFee > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery Fee</span>
                  <span>₹{order.pricing.deliveryFee}</span>
                </div>
              )}
              {order.pricing.platformFee && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Platform Fee</span>
                  <span>₹{order.pricing.platformFee}</span>
                </div>
              )}
              {order.pricing.discount && (
                <div className="flex justify-between text-green-600">
                  <span>Discount</span>
                  <span>-₹{order.pricing.discount}</span>
                </div>
              )}
              <div className="border-t pt-3">
                <div className="flex justify-between font-bold text-lg">
                  <span>Total Amount</span>
                  <span>₹{order.pricing.total}</span>
                </div>
              </div>
              <div className="flex items-center justify-between pt-2">
                <span className="text-gray-600">Payment Status</span>
                <Badge className={
                  order.paymentStatus === 'paid' 
                    ? 'bg-green-100 text-green-800' 
                    : order.paymentStatus === 'failed'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }>
                  {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                </Badge>
              </div>
              {order.paymentMethod && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Method</span>
                  <span>{order.paymentMethod}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Special Instructions */}
        {order.specialInstructions && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Special Instructions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">{order.specialInstructions}</p>
            </CardContent>
          </Card>
        )}

        {/* Platform Specific Information */}

        {order.platform === 'own-app' && 'ownAppData' in order && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🏪 Restaurant App Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {order.ownAppData.tableNumber && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Table</span>
                  <span>{order.ownAppData.tableNumber}</span>
                </div>
              )}
              {order.ownAppData.waiterAssigned && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Waiter</span>
                  <span>{order.ownAppData.waiterAssigned}</span>
                </div>
              )}
              {order.ownAppData.kotNumber && (
                <div className="flex justify-between">
                  <span className="text-gray-600">KOT Number</span>
                  <span>{order.ownAppData.kotNumber}</span>
                </div>
              )}
              {order.ownAppData.loyaltyPoints !== undefined && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Loyalty Points</span>
                  <span>{order.ownAppData.loyaltyPoints}</span>
                </div>
              )}
              {order.ownAppData.isRegularCustomer && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Regular Customer</span>
                  <Badge className="bg-blue-100 text-blue-800">Yes</Badge>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default MultiPlatformOrderDetails;
