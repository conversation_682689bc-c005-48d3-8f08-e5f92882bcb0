import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  DollarSign,
  Calendar,
  Clock,
  User,
  Phone,
  CreditCard,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import {
  getSalaryRecordById,
  formatMonth,
  type SalaryRecord
} from "@/utils/salaryStorage";

const SalaryDetails = () => {
  const navigate = useNavigate();
  const { recordId } = useParams();
  const [salaryRecord, setSalaryRecord] = useState<SalaryRecord | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (recordId) {
      loadSalaryRecord(recordId);
    }
  }, [recordId]);

  const loadSalaryRecord = (id: string) => {
    try {
      const record = getSalaryRecordById(id);
      setSalaryRecord(record);
    } catch (error) {
      console.error("Error loading salary record:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'partial': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'pending': return <Clock className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'partial': return 'bg-yellow-100 text-yellow-800';
      case 'pending': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="apk-page-container bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading salary details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!salaryRecord) {
    return (
      <div className="apk-page-container bg-gray-50">
        <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate("/admin/salary-management")}
                className="text-white hover:bg-white/20 transition-all duration-200"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-white">Salary Details</h1>
                <p className="text-white/80 text-sm">Record not found</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="p-4 apk-content-with-header">
          <div className="text-center py-8">
            <DollarSign className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Salary record not found</p>
            <Button 
              onClick={() => navigate("/admin/salary-management")}
              className="mt-4"
            >
              Back to Salary Management
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const balance = salaryRecord.finalSalary - salaryRecord.paidAmount;

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/salary-management")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">Salary Details</h1>
              <p className="text-white/80 text-sm">{salaryRecord.staffName}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Staff Information */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <User className="h-5 w-5" />
              Staff Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Name</p>
                  <p className="font-medium">{salaryRecord.staffName}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Phone</p>
                  <p className="font-medium">{salaryRecord.staffPhone}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Badge className="bg-blue-100 text-blue-800 capitalize">
                  {salaryRecord.staffRole}
                </Badge>
              </div>
              <div className="flex items-center gap-3">
                {getStatusIcon(salaryRecord.status)}
                <Badge className={`${getStatusColor(salaryRecord.status)} capitalize`}>
                  {salaryRecord.status}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Salary Breakdown */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Salary Breakdown - {formatMonth(salaryRecord.month)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-600 mb-1">Base Salary</p>
                <p className="text-2xl font-bold text-blue-800">₹{salaryRecord.baseSalary.toLocaleString()}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-600 mb-1">Final Salary</p>
                <p className="text-2xl font-bold text-green-800">₹{salaryRecord.finalSalary.toLocaleString()}</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <p className="text-sm text-purple-600 mb-1">Paid Amount</p>
                <p className="text-2xl font-bold text-purple-800">₹{salaryRecord.paidAmount.toLocaleString()}</p>
              </div>
              <div className={`p-4 rounded-lg ${balance > 0 ? 'bg-red-50' : 'bg-green-50'}`}>
                <p className={`text-sm mb-1 ${balance > 0 ? 'text-red-600' : 'text-green-600'}`}>Balance</p>
                <p className={`text-2xl font-bold ${balance > 0 ? 'text-red-800' : 'text-green-800'}`}>
                  ₹{balance.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance & Performance */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Attendance & Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-500">Working Days</p>
                <p className="text-xl font-bold text-gray-900">{salaryRecord.workingDays}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Present Days</p>
                <p className="text-xl font-bold text-green-600">{salaryRecord.presentDays}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Total Hours</p>
                <p className="text-xl font-bold text-blue-600">{salaryRecord.totalHours}h</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Overtime</p>
                <p className="text-xl font-bold text-orange-600">{salaryRecord.overtimeHours}h</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Details */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Additional Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <p className="text-sm text-green-600">Bonuses</p>
                </div>
                <p className="text-xl font-bold text-green-800">₹{salaryRecord.bonuses.toLocaleString()}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingDown className="h-4 w-4 text-red-600" />
                  <p className="text-sm text-red-600">Deductions</p>
                </div>
                <p className="text-xl font-bold text-red-800">₹{salaryRecord.deductions.toLocaleString()}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-gray-600" />
                  <p className="text-sm text-gray-600">Generated On</p>
                </div>
                <p className="text-sm font-medium text-gray-800">
                  {new Date(salaryRecord.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SalaryDetails;
