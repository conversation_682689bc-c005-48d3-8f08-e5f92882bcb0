import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  ArrowLeft, 
  DollarSign,
  Users,
  Plus,
  Search,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle,
  CreditCard,
  Calendar
} from "lucide-react";
import {
  getAllAdvancePayments,
  createAdvancePayment,
  getActiveAdvancesForStaff,
  getTotalActiveAdvances,
  type AdvancePayment
} from "@/utils/salaryStorage";
import { getAllStaffPins } from "@/utils/staffPinStorage";
import { useToast } from "@/hooks/use-toast";

const AdvanceManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [advancePayments, setAdvancePayments] = useState<AdvancePayment[]>([]);
  const [filteredAdvances, setFilteredAdvances] = useState<AdvancePayment[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [staffList, setStaffList] = useState<any[]>([]);
  const [isAddAdvanceDialogOpen, setIsAddAdvanceDialogOpen] = useState(false);
  const [advanceData, setAdvanceData] = useState({
    staffId: "",
    amount: "",
    paymentMethod: "cash" as AdvancePayment['paymentMethod'],
    reason: "",
    notes: ""
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterAdvances();
  }, [advancePayments, searchTerm, selectedStatus]);

  const loadData = () => {
    // Load advance payments
    const advances = getAllAdvancePayments();
    setAdvancePayments(advances);

    // Load staff list
    const staff = getAllStaffPins().filter(s => s.isActive);
    setStaffList(staff);
  };

  const filterAdvances = () => {
    let filtered = advancePayments;

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(advance => advance.status === selectedStatus);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(advance => 
        advance.staffName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        advance.staffPhone.includes(searchTerm)
      );
    }

    // Sort by payment date (newest first)
    filtered.sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime());

    setFilteredAdvances(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'deducted':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Clock className="h-4 w-4" />;
      case 'deducted':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const handleAddAdvance = async () => {
    const amount = parseFloat(advanceData.amount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid advance amount",
        variant: "destructive",
      });
      return;
    }

    if (!advanceData.staffId) {
      toast({
        title: "Staff Required",
        description: "Please select a staff member",
        variant: "destructive",
      });
      return;
    }

    try {
      const selectedStaff = staffList.find(s => s.id === advanceData.staffId);
      if (!selectedStaff) {
        throw new Error("Selected staff not found");
      }

      createAdvancePayment(
        advanceData.staffId,
        selectedStaff.name,
        selectedStaff.phone,
        amount,
        advanceData.paymentMethod,
        "admin", // In real app, this would be the logged-in admin's ID
        advanceData.reason || undefined,
        advanceData.notes || undefined
      );

      toast({
        title: "Advance Payment Added",
        description: `₹${amount} advance given to ${selectedStaff.name}`,
      });

      // Reset form and reload data
      setAdvanceData({
        staffId: "",
        amount: "",
        paymentMethod: "cash",
        reason: "",
        notes: ""
      });
      setIsAddAdvanceDialogOpen(false);
      loadData();
    } catch (error) {
      toast({
        title: "Failed to Add Advance",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const getAdvanceSummary = () => {
    const activeAdvances = advancePayments.filter(a => a.status === 'active');
    const totalActive = activeAdvances.reduce((sum, advance) => sum + advance.amount, 0);
    const deductedAdvances = advancePayments.filter(a => a.status === 'deducted');
    const totalDeducted = deductedAdvances.reduce((sum, advance) => sum + advance.amount, 0);

    return {
      totalActive,
      totalDeducted,
      activeCount: activeAdvances.length,
      deductedCount: deductedAdvances.length
    };
  };

  const summary = getAdvanceSummary();

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/salary-management")}
              className="text-white hover:bg-white/20 transition-all duration-200 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-white truncate">Advance Management</h1>
              <p className="text-white/80 text-xs sm:text-sm truncate">Manage staff advance payments</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsAddAdvanceDialogOpen(true)}
            className="text-white hover:bg-white/20 shrink-0 px-2 sm:px-3"
          >
            <Plus className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Add Advance</span>
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">₹{summary.totalActive.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Active Advances</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-blue-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">₹{summary.totalDeducted.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Deducted</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-amber-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Users className="h-6 w-6 text-amber-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{summary.activeCount}</p>
                <p className="text-sm text-gray-500">Active Records</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-purple-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{summary.deductedCount}</p>
                <p className="text-sm text-gray-500">Deducted Records</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="shadow-sm border-0 bg-white">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search staff..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="deducted">Deducted</option>
                <option value="cancelled">Cancelled</option>
              </select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedStatus("all");
                }}
                className="w-full"
              >
                <Filter className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Advance Records */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Advance Records ({filteredAdvances.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {filteredAdvances.length === 0 ? (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No advance records found</p>
                <p className="text-sm text-gray-400">Add advance payments to see records here</p>
              </div>
            ) : (
              filteredAdvances.map((advance) => (
                <div
                  key={advance.id}
                  className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2 flex-wrap">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(advance.status)}
                          <span className="font-semibold text-gray-900">
                            {advance.staffName}
                          </span>
                        </div>
                        <Badge className={`${getStatusColor(advance.status)} text-xs px-2 py-1`}>
                          {advance.status.toUpperCase()}
                        </Badge>
                        <span className="text-lg font-bold text-green-600">
                          ₹{advance.amount.toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Date:</span>
                          <span className="font-medium ml-2">
                            {new Date(advance.paymentDate).toLocaleDateString('en-IN')}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Method:</span>
                          <span className="font-medium ml-2 capitalize">
                            {advance.paymentMethod.replace('_', ' ')}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Reason:</span>
                          <span className="font-medium ml-2">
                            {advance.reason || 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Paid By:</span>
                          <span className="font-medium ml-2 capitalize">
                            {advance.paidBy}
                          </span>
                        </div>
                      </div>
                      
                      {advance.notes && (
                        <div className="mt-2 text-sm">
                          <span className="text-gray-500">Notes:</span>
                          <span className="ml-2 text-gray-700">{advance.notes}</span>
                        </div>
                      )}

                      {advance.status === 'deducted' && advance.deductedDate && (
                        <div className="mt-2 text-sm">
                          <span className="text-gray-500">Deducted on:</span>
                          <span className="ml-2 text-blue-600 font-medium">
                            {new Date(advance.deductedDate).toLocaleDateString('en-IN')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* Add Advance Dialog */}
      <Dialog open={isAddAdvanceDialogOpen} onOpenChange={setIsAddAdvanceDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Advance Payment</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Select Staff</label>
              <select
                value={advanceData.staffId}
                onChange={(e) => setAdvanceData({...advanceData, staffId: e.target.value})}
                className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="">Select staff member</option>
                {staffList.map(staff => (
                  <option key={staff.id} value={staff.id}>
                    {staff.name} ({staff.role})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Advance Amount</label>
              <Input
                type="number"
                placeholder="Enter amount"
                value={advanceData.amount}
                onChange={(e) => setAdvanceData({...advanceData, amount: e.target.value})}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Payment Method</label>
              <select
                value={advanceData.paymentMethod}
                onChange={(e) => setAdvanceData({...advanceData, paymentMethod: e.target.value as any})}
                className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="cash">Cash</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="upi">UPI</option>
                <option value="cheque">Cheque</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Reason</label>
              <Input
                placeholder="Reason for advance"
                value={advanceData.reason}
                onChange={(e) => setAdvanceData({...advanceData, reason: e.target.value})}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Notes (Optional)</label>
              <Input
                placeholder="Additional notes"
                value={advanceData.notes}
                onChange={(e) => setAdvanceData({...advanceData, notes: e.target.value})}
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsAddAdvanceDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddAdvance}
                className="flex-1"
              >
                Add Advance
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdvanceManagement;
