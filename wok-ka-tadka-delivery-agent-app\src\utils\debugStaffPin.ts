// Debug utility for Staff PIN issues
import { 
  getAllStaffPins, 
  createStaffPin, 
  validateStaffPin, 
  initializeDefaultPins 
} from './staffPinStorage';

export const debugStaffPinIssue = () => {
  console.log("🐛 DEBUG: Staff PIN Issue Investigation");
  console.log("=" .repeat(50));
  
  // Initialize default PINs
  initializeDefaultPins();
  
  // Check current state
  const allPins = getAllStaffPins();
  console.log("📋 Current staff PINs in storage:", allPins);
  
  // Test admin PIN
  console.log("\n🔐 Testing Admin PIN:");
  const adminTest = validateStaffPin("1234567890", "0000");
  console.log("Admin validation result:", adminTest);
  
  // Create a test staff member
  console.log("\n👤 Creating test staff member:");
  try {
    const testStaff = createStaffPin({
      name: "Debug Test Waiter",
      phone: "9999999999",
      role: "waiter",
      createdBy: "debug"
    });
    console.log("✅ Test staff created:", testStaff);
    
    // Test validation immediately
    console.log("\n🔍 Testing validation immediately:");
    const immediateTest = validateStaffPin("9999999999", testStaff.pin);
    console.log("Immediate validation result:", immediateTest);
    
    // Check storage again
    const updatedPins = getAllStaffPins();
    console.log("\n📋 Updated staff PINs:", updatedPins);
    
    // Test with different phone formats
    console.log("\n📱 Testing phone number formats:");
    console.log("Original phone:", testStaff.phone);
    console.log("Test 1 - Exact match:", validateStaffPin(testStaff.phone, testStaff.pin));
    console.log("Test 2 - With spaces:", validateStaffPin(testStaff.phone.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3'), testStaff.pin));
    console.log("Test 3 - With +91:", validateStaffPin("+91" + testStaff.phone, testStaff.pin));
    
  } catch (error) {
    console.error("❌ Error creating test staff:", error);
  }
  
  console.log("\n" + "=".repeat(50));
  console.log("🏁 Debug investigation complete");
};

export const testSpecificPin = (phone: string, pin: string) => {
  console.log(`🔍 Testing specific PIN: ${phone} / ${pin}`);
  
  const allPins = getAllStaffPins();
  console.log("📋 All PINs:", allPins);
  
  const result = validateStaffPin(phone, pin);
  console.log("✅ Result:", result);
  
  // Manual check
  const manualCheck = allPins.find(p => {
    console.log(`Checking: ${p.phone} === ${phone} && ${p.pin} === ${pin} && ${p.isActive}`);
    return p.phone === phone && p.pin === pin && p.isActive;
  });
  console.log("🔍 Manual check result:", manualCheck);
  
  return result;
};

export const cleanupTestData = () => {
  const allPins = getAllStaffPins();
  const cleanedPins = allPins.filter(pin => !pin.name.includes("Debug Test"));
  localStorage.setItem('wok_ka_tadka_staff_pins', JSON.stringify(cleanedPins));
  console.log("🧹 Test data cleaned up");
};

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).debugStaffPin = debugStaffPinIssue;
  (window as any).testSpecificPin = testSpecificPin;
  (window as any).cleanupTestData = cleanupTestData;
  (window as any).getAllStaffPins = getAllStaffPins;
  (window as any).validateStaffPin = validateStaffPin;
}
