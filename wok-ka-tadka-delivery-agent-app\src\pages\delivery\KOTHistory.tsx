import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  FileText,
  Clock,
  Calendar,
  Users,
  Utensils,
  ChevronRight,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { kotStorage, KOT } from "@/utils/kotStorage";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";

const KOTHistory = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [kotHistory, setKotHistory] = useState<KOT[]>([]);
  const [filteredHistory, setFilteredHistory] = useState<KOT[]>([]);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>("");

  // Load KOT history on component mount
  useEffect(() => {
    const allKOTs = kotStorage.getAllKOTs();
    setKotHistory(allKOTs);
    setFilteredHistory(allKOTs);
  }, []);

  // Filter KOTs based on status and search query
  useEffect(() => {
    let filtered = kotHistory;
    
    // Apply status filter
    if (filter !== "all") {
      filtered = filtered.filter(kot => kot.status === filter);
    }
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(kot =>
        kot.kotNumber.toLowerCase().includes(query) ||
        kot.tableId.toLowerCase().includes(query)
      );
    }

    // Apply date filter
    if (selectedDate) {
      filtered = filtered.filter(kot => {
        const kotDate = format(new Date(kot.createdAt), "yyyy-MM-dd");
        return kotDate === selectedDate;
      });
    }

    setFilteredHistory(filtered);
  }, [filter, searchQuery, kotHistory, selectedDate]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today, ${format(date, "h:mm a")}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${format(date, "h:mm a")}`;
    } else {
      return format(date, "dd MMM yyyy, h:mm a");
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "completed":
        return "Completed";
      case "cancelled":
        return "Cancelled";
      default:
        return status;
    }
  };

  // View KOT details
  const viewKOTDetails = (kot: KOT) => {
    try {
      console.log('Navigating to KOT details:', kot);
      navigate(`/delivery/kot-details/${kot.kotNumber}`, {
        state: {
          existingKOT: kot
        }
      });
    } catch (error) {
      console.error('Error navigating to KOT details:', error);
      toast({
        title: "Navigation Error",
        description: "Unable to view KOT details. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Calculate summary statistics
  const getStats = () => {
    const total = filteredHistory.length;
    const active = filteredHistory.filter(kot => kot.status === 'active').length;
    const completed = filteredHistory.filter(kot => kot.status === 'completed').length;
    const cancelled = filteredHistory.filter(kot => kot.status === 'cancelled').length;
    const totalRevenue = filteredHistory
      .filter(kot => kot.status === 'completed')
      .reduce((sum, kot) => sum + kot.totalAmount, 0);

    return { total, active, completed, cancelled, totalRevenue };
  };

  const stats = getStats();

  // Complete KOT
  const completeKOT = (kotNumber: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const success = kotStorage.completeKOT(kotNumber);
    if (success) {
      const updatedKOTs = kotStorage.getAllKOTs();
      setKotHistory(updatedKOTs);
      toast({
        title: "KOT Completed",
        description: `KOT #${kotNumber} has been marked as completed.`,
      });
    }
  };

  // Cancel KOT
  const cancelKOT = (kotNumber: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const success = kotStorage.cancelKOT(kotNumber);
    if (success) {
      const updatedKOTs = kotStorage.getAllKOTs();
      setKotHistory(updatedKOTs);
      toast({
        title: "KOT Cancelled",
        description: `KOT #${kotNumber} has been cancelled.`,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white">KOT History</h1>
              <p className="text-white/80 text-sm">All kitchen order tickets</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/20"
            onClick={() => setShowFilterModal(!showFilterModal)}
          >
            <Filter className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Search Bar */}
        <div className="px-4 pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input 
              placeholder="Search by KOT number or table" 
              className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        {/* Filter Tabs */}
        <div className="px-4 pb-4">
          <Tabs defaultValue="all" value={filter} onValueChange={setFilter}>
            <TabsList className="bg-white/10 w-full">
              <TabsTrigger value="all" className="text-white data-[state=active]:bg-white data-[state=active]:text-primary">
                All
              </TabsTrigger>
              <TabsTrigger value="active" className="text-white data-[state=active]:bg-white data-[state=active]:text-primary">
                Active
              </TabsTrigger>
              <TabsTrigger value="completed" className="text-white data-[state=active]:bg-white data-[state=active]:text-primary">
                Completed
              </TabsTrigger>
              <TabsTrigger value="cancelled" className="text-white data-[state=active]:bg-white data-[state=active]:text-primary">
                Cancelled
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="p-4 space-y-4 apk-content-with-tall-header">
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-xs text-blue-800">Total KOTs</div>
            </CardContent>
          </Card>
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
              <div className="text-xs text-green-800">Completed</div>
            </CardContent>
          </Card>
          <Card className="bg-yellow-50 border-yellow-200">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.active}</div>
              <div className="text-xs text-yellow-800">Active</div>
            </CardContent>
          </Card>
          <Card className="bg-purple-50 border-purple-200">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-purple-600">₹{stats.totalRevenue}</div>
              <div className="text-xs text-purple-800">Revenue</div>
            </CardContent>
          </Card>
        </div>

        {/* Date Filter */}
        <Card className="bg-white border-gray-200">
          <CardContent className="p-3">
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="flex-1 text-sm"
                placeholder="Filter by date"
              />
              {selectedDate && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedDate("")}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Clear
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* KOT History Cards */}
        {filteredHistory.length > 0 ? (
          filteredHistory.map((kot) => (
            <Card 
              key={kot.kotNumber} 
              className="shadow-sm border-0 bg-white hover:shadow-md transition-shadow"
              onClick={() => viewKOTDetails(kot)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-gray-900">KOT #{kot.kotNumber}</span>
                    <Badge className={`${getStatusColor(kot.status)} text-xs px-2 py-1`}>
                      {getStatusText(kot.status)}
                    </Badge>
                  </div>
                  <p className="font-bold text-lg text-primary">₹{kot.totalAmount}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Users className="h-4 w-4" />
                    <span>Table {kot.tableId}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Utensils className="h-4 w-4" />
                    <span>{kot.items.length} items</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>{format(new Date(kot.createdAt), "dd MMM yyyy")}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>{format(new Date(kot.createdAt), "h:mm a")}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="text-gray-600">
                    {kot.versions.length > 1 ? (
                      <span className="text-blue-600 font-medium">{kot.versions.length} versions</span>
                    ) : (
                      <span>Created {formatDate(kot.createdAt)}</span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {kot.status === 'active' && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => completeKOT(kot.kotNumber, e)}
                          className="text-green-600 hover:text-green-700 hover:bg-green-50 p-1 h-auto"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => cancelKOT(kot.kotNumber, e)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1 h-auto"
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <div className="flex items-center text-primary font-medium">
                      <span>View Details</span>
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-10">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-1">No KOT History Found</h3>
            <p className="text-gray-600">
              {searchQuery ? "Try a different search term" : "No kitchen order tickets have been generated yet"}
            </p>
          </div>
        )}
      </div>

      {/* Filter Modal */}
      <Dialog open={showFilterModal} onOpenChange={setShowFilterModal}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-primary" />
              Filter KOTs
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Status Filter */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Status</h4>
              <div className="space-y-2">
                <Button
                  variant={filter === "all" ? "default" : "outline"}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setFilter("all");
                    setShowFilterModal(false);
                  }}
                >
                  All KOTs
                </Button>
                <Button
                  variant={filter === "active" ? "default" : "outline"}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setFilter("active");
                    setShowFilterModal(false);
                  }}
                >
                  Active Only
                </Button>
                <Button
                  variant={filter === "completed" ? "default" : "outline"}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setFilter("completed");
                    setShowFilterModal(false);
                  }}
                >
                  Completed Only
                </Button>
                <Button
                  variant={filter === "cancelled" ? "default" : "outline"}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setFilter("cancelled");
                    setShowFilterModal(false);
                  }}
                >
                  Cancelled Only
                </Button>
              </div>
            </div>

            {/* Date Filter */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Date Filter</h4>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full"
              />
              {selectedDate && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedDate("")}
                  className="w-full mt-2 text-gray-500 hover:text-gray-700"
                >
                  Clear Date Filter
                </Button>
              )}
            </div>

            {/* Quick Actions */}
            <div className="pt-4 border-t">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => {
                  setFilter("all");
                  setSelectedDate("");
                  setSearchQuery("");
                  setShowFilterModal(false);
                }}
              >
                Clear All Filters
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KOTHistory;
